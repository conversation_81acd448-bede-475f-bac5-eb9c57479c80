"""
Tests for semantic space functionality.
"""

import pytest
import tempfile
import shutil
from pathlib import Path
from unittest.mock import Mock, patch

from paper_hgen.semantic_space import DenseSpace, SparseSpace, SemanticSpaceManager
from paper_hgen.models import LiteratureDocument, PaperMetadata, ResearchProblem, ResearchMethod


class TestSparseSpace:
    """Test SparseSpace (BM25) functionality."""
    
    def test_create_sparse_space(self):
        """Test creating a sparse space."""
        space = SparseSpace()
        assert space.space_type == "sparse"
        assert space.bm25 is None
        assert space.tokenized_docs == []
    
    def test_preprocess_text(self):
        """Test text preprocessing."""
        space = SparseSpace()
        text = "This is a TEST document with STOPWORDS and numbers 123!"
        tokens = space._preprocess_text(text)
        
        # Should be lowercase, stemmed, and without stopwords
        assert all(token.islower() for token in tokens if token.isalpha())
        assert "test" in [space.stemmer.stem("test")]  # Should be stemmed
        assert len(tokens) > 0
    
    def test_build_and_search(self):
        """Test building index and searching."""
        space = SparseSpace()
        
        # Sample documents
        documents = [
            {"content": "machine learning algorithms for student prediction"},
            {"content": "deep learning neural networks in education"},
            {"content": "statistical analysis of learning outcomes"},
            {"content": "collaborative learning in online environments"}
        ]
        
        # Build index
        space.build_index(documents)
        assert space.bm25 is not None
        assert len(space.tokenized_docs) == 4
        
        # Search
        results = space.search("machine learning", top_k=2)
        assert len(results) <= 2
        assert all(isinstance(result, tuple) for result in results)
        assert all(len(result) == 2 for result in results)  # (doc_id, score)
    
    def test_save_and_load(self):
        """Test saving and loading sparse space."""
        space = SparseSpace()
        
        documents = [
            {"content": "test document one"},
            {"content": "test document two"}
        ]
        space.build_index(documents)
        
        # Save to temporary directory
        with tempfile.TemporaryDirectory() as temp_dir:
            space.save(temp_dir)
            
            # Create new space and load
            new_space = SparseSpace()
            new_space.load(temp_dir)
            
            # Test that loaded space works
            results = new_space.search("test", top_k=2)
            assert len(results) == 2


class TestDenseSpace:
    """Test DenseSpace functionality."""
    
    @patch('paper_hgen.semantic_space.SentenceTransformer')
    def test_create_dense_space(self, mock_transformer):
        """Test creating a dense space."""
        # Mock the sentence transformer
        mock_model = Mock()
        mock_model.get_sentence_embedding_dimension.return_value = 768
        mock_transformer.return_value = mock_model
        
        space = DenseSpace("test-model")
        assert space.space_type == "dense"
        assert space.model_name == "test-model"
        assert space.index is None
    
    @patch('paper_hgen.semantic_space.SentenceTransformer')
    @patch('paper_hgen.semantic_space.faiss')
    def test_build_and_search(self, mock_faiss, mock_transformer):
        """Test building index and searching."""
        # Mock sentence transformer
        mock_model = Mock()
        mock_model.get_sentence_embedding_dimension.return_value = 768
        mock_model.encode.return_value = [[0.1] * 768, [0.2] * 768]
        mock_transformer.return_value = mock_model
        
        # Mock FAISS
        mock_index = Mock()
        mock_index.ntotal = 2
        mock_index.search.return_value = ([[0.9, 0.8]], [[0, 1]])
        mock_faiss.IndexFlatIP.return_value = mock_index
        
        space = DenseSpace("test-model")
        
        documents = [
            {"content": "test document one"},
            {"content": "test document two"}
        ]
        
        # Build index
        space.build_index(documents)
        assert space.index is not None
        
        # Search
        results = space.search("test query", top_k=2)
        assert len(results) == 2
        assert results[0][0] == 0  # First document index
        assert results[0][1] == 0.9  # First document score


class TestSemanticSpaceManager:
    """Test SemanticSpaceManager functionality."""
    
    def test_create_manager(self):
        """Test creating a semantic space manager."""
        manager = SemanticSpaceManager("test-model")
        assert manager.dense_model == "test-model"
        assert len(manager.spaces) == 4  # 2 content types × 2 space types
        assert "problems_dense" in manager.spaces
        assert "problems_sparse" in manager.spaces
        assert "methods_dense" in manager.spaces
        assert "methods_sparse" in manager.spaces
    
    def test_get_space(self):
        """Test getting specific spaces."""
        manager = SemanticSpaceManager()
        
        problems_dense = manager.get_space("dense", "problems")
        assert problems_dense is not None
        assert isinstance(problems_dense, DenseSpace)
        
        methods_sparse = manager.get_space("sparse", "methods")
        assert methods_sparse is not None
        assert isinstance(methods_sparse, SparseSpace)
        
        # Test invalid space
        invalid_space = manager.get_space("invalid", "problems")
        assert invalid_space is None
    
    @patch('paper_hgen.semantic_space.SentenceTransformer')
    def test_build_from_literature(self, mock_transformer):
        """Test building spaces from literature documents."""
        # Mock sentence transformer
        mock_model = Mock()
        mock_model.get_sentence_embedding_dimension.return_value = 768
        mock_model.encode.return_value = [[0.1] * 768]
        mock_transformer.return_value = mock_model
        
        manager = SemanticSpaceManager("test-model")
        
        # Create sample literature documents
        metadata = PaperMetadata(title="Test Paper", authors=["Author"])
        
        problem = ResearchProblem(
            id="prob_1",
            text="How to improve learning?",
            paper_id="paper_1",
            confidence=0.9
        )
        
        method = ResearchMethod(
            id="method_1",
            name="Machine Learning",
            description="Using ML for analysis",
            methodology_type="quantitative",
            paper_id="paper_1",
            confidence=0.8
        )
        
        doc = LiteratureDocument(
            metadata=metadata,
            research_problems=[problem],
            research_methods=[method]
        )
        
        # Build spaces
        with patch('paper_hgen.semantic_space.faiss'):
            manager.build_from_literature([doc])
        
        # Check that spaces have documents
        problems_dense = manager.get_space("dense", "problems")
        assert len(problems_dense.documents) == 1
        
        methods_dense = manager.get_space("dense", "methods")
        assert len(methods_dense.documents) == 1
    
    def test_save_and_load_all(self):
        """Test saving and loading all spaces."""
        manager = SemanticSpaceManager()
        
        # Mock some data in spaces
        for space in manager.spaces.values():
            space.documents = [{"content": "test"}]
            space.metadata = [{"id": "test"}]
        
        with tempfile.TemporaryDirectory() as temp_dir:
            # Save all spaces
            manager.save_all(temp_dir)
            
            # Check that directories were created
            base_path = Path(temp_dir)
            for space_name in manager.spaces.keys():
                assert (base_path / space_name).exists()
            
            # Create new manager and load
            new_manager = SemanticSpaceManager()
            new_manager.load_all(temp_dir)
            
            # Check that data was loaded
            for space_name, space in new_manager.spaces.items():
                if hasattr(space, 'documents'):
                    assert len(space.documents) > 0


@pytest.fixture
def sample_documents():
    """Fixture providing sample documents for testing."""
    return [
        {
            "content": "machine learning algorithms for predicting student performance",
            "metadata": {"id": "doc1", "keywords": ["machine learning", "prediction"]}
        },
        {
            "content": "collaborative learning in online educational environments",
            "metadata": {"id": "doc2", "keywords": ["collaborative", "online learning"]}
        },
        {
            "content": "deep learning neural networks for educational data mining",
            "metadata": {"id": "doc3", "keywords": ["deep learning", "data mining"]}
        }
    ]


def test_integration_sparse_search(sample_documents):
    """Integration test for sparse search functionality."""
    space = SparseSpace()
    space.build_index(sample_documents)
    
    # Test various queries
    queries = [
        "machine learning",
        "collaborative learning",
        "neural networks",
        "educational data"
    ]
    
    for query in queries:
        results = space.search(query, top_k=3)
        assert len(results) <= 3
        assert all(score >= 0 for _, score in results)
        
        # Results should be sorted by score (descending)
        scores = [score for _, score in results]
        assert scores == sorted(scores, reverse=True)
