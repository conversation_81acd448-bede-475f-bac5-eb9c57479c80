# Core dependencies
torch>=2.0.0
transformers>=4.30.0
sentence-transformers>=2.2.0
openai>=1.0.0
numpy>=1.24.0
pandas>=2.0.0
scikit-learn>=1.3.0

# PDF processing
PyPDF2>=3.0.0
pdfplumber>=0.9.0

# Search and retrieval
faiss-cpu>=1.7.4
rank-bm25>=0.2.2

# Data processing
tqdm>=4.65.0
nltk>=3.8.0
spacy>=3.6.0

# Configuration and utilities
pydantic>=2.0.0
python-dotenv>=1.0.0
pyyaml>=6.0.0
click>=8.1.0

# Web interface (optional)
fastapi>=0.100.0
uvicorn>=0.23.0
streamlit>=1.25.0

# Development and testing
pytest>=7.4.0
pytest-asyncio>=0.21.0
black>=23.0.0
flake8>=6.0.0
