"""
Tests for data models.
"""

import pytest
from datetime import datetime
from paper_hgen.models import (
    PaperMetadata,
    ResearchProblem,
    ResearchMethod,
    LiteratureDocument,
    HypothesisRequest,
    HypothesisResponse,
    SearchResult,
    ResearchDomain
)


class TestPaperMetadata:
    """Test PaperMetadata model."""
    
    def test_create_basic_metadata(self):
        """Test creating basic paper metadata."""
        metadata = PaperMetadata(
            title="Test Paper",
            authors=["Author 1", "Author 2"],
            year=2023
        )
        
        assert metadata.title == "Test Paper"
        assert metadata.authors == ["Author 1", "Author 2"]
        assert metadata.year == 2023
        assert metadata.domain == ResearchDomain.OTHER
        assert isinstance(metadata.extracted_at, datetime)
    
    def test_metadata_with_all_fields(self):
        """Test creating metadata with all fields."""
        metadata = PaperMetadata(
            title="Complete Paper",
            authors=["Author 1"],
            year=2023,
            journal="Test Journal",
            doi="10.1000/test",
            abstract="Test abstract",
            keywords=["keyword1", "keyword2"],
            domain=ResearchDomain.LEARNING_ANALYTICS,
            file_path="/path/to/file.pdf"
        )
        
        assert metadata.journal == "Test Journal"
        assert metadata.doi == "10.1000/test"
        assert metadata.abstract == "Test abstract"
        assert metadata.keywords == ["keyword1", "keyword2"]
        assert metadata.domain == ResearchDomain.LEARNING_ANALYTICS


class TestResearchProblem:
    """Test ResearchProblem model."""
    
    def test_create_research_problem(self):
        """Test creating a research problem."""
        problem = ResearchProblem(
            id="test_problem_1",
            text="How can we improve student engagement?",
            context="This is important for online learning",
            keywords=["engagement", "online learning"],
            paper_id="test_paper",
            confidence=0.9
        )
        
        assert problem.id == "test_problem_1"
        assert problem.text == "How can we improve student engagement?"
        assert problem.context == "This is important for online learning"
        assert problem.keywords == ["engagement", "online learning"]
        assert problem.paper_id == "test_paper"
        assert problem.confidence == 0.9
        assert problem.domain == ResearchDomain.OTHER
    
    def test_confidence_validation(self):
        """Test confidence score validation."""
        # Valid confidence
        problem = ResearchProblem(
            id="test",
            text="Test problem",
            paper_id="test_paper",
            confidence=0.5
        )
        assert problem.confidence == 0.5
        
        # Test boundary values
        problem_min = ResearchProblem(
            id="test_min",
            text="Test problem",
            paper_id="test_paper",
            confidence=0.0
        )
        assert problem_min.confidence == 0.0
        
        problem_max = ResearchProblem(
            id="test_max",
            text="Test problem",
            paper_id="test_paper",
            confidence=1.0
        )
        assert problem_max.confidence == 1.0


class TestResearchMethod:
    """Test ResearchMethod model."""
    
    def test_create_research_method(self):
        """Test creating a research method."""
        method = ResearchMethod(
            id="test_method_1",
            name="Machine Learning Analysis",
            description="Using ML to analyze student data",
            methodology_type="quantitative",
            tools_used=["Python", "scikit-learn"],
            data_sources=["student logs", "assessments"],
            paper_id="test_paper",
            confidence=0.85
        )
        
        assert method.id == "test_method_1"
        assert method.name == "Machine Learning Analysis"
        assert method.description == "Using ML to analyze student data"
        assert method.methodology_type == "quantitative"
        assert method.tools_used == ["Python", "scikit-learn"]
        assert method.data_sources == ["student logs", "assessments"]
        assert method.paper_id == "test_paper"
        assert method.confidence == 0.85


class TestLiteratureDocument:
    """Test LiteratureDocument model."""
    
    def test_create_literature_document(self):
        """Test creating a complete literature document."""
        metadata = PaperMetadata(
            title="Test Paper",
            authors=["Author 1"],
            year=2023
        )
        
        problem = ResearchProblem(
            id="problem_1",
            text="Test problem",
            paper_id="test_paper",
            confidence=0.9
        )
        
        method = ResearchMethod(
            id="method_1",
            name="Test Method",
            description="Test description",
            methodology_type="quantitative",
            paper_id="test_paper",
            confidence=0.8
        )
        
        doc = LiteratureDocument(
            metadata=metadata,
            research_problems=[problem],
            research_methods=[method],
            full_text="Full paper text here",
            sections={"abstract": "Abstract text", "introduction": "Intro text"}
        )
        
        assert doc.metadata.title == "Test Paper"
        assert len(doc.research_problems) == 1
        assert len(doc.research_methods) == 1
        assert doc.full_text == "Full paper text here"
        assert "abstract" in doc.sections
        assert "introduction" in doc.sections
    
    def test_paper_id_property(self):
        """Test paper_id property generation."""
        metadata = PaperMetadata(
            title="A Very Long Paper Title That Should Be Truncated",
            authors=["Author 1"],
            year=2023
        )
        
        doc = LiteratureDocument(metadata=metadata)
        paper_id = doc.paper_id
        
        assert len(paper_id.split('_')[0]) <= 50  # Title part should be truncated
        assert paper_id.endswith("_2023")


class TestHypothesisRequest:
    """Test HypothesisRequest model."""
    
    def test_create_hypothesis_request(self):
        """Test creating a hypothesis request."""
        request = HypothesisRequest(
            user_idea="How can we predict student success?",
            domain=ResearchDomain.LEARNING_ANALYTICS,
            max_results=15,
            search_weights={"dense": 0.8, "sparse": 0.2},
            generation_params={"temperature": 0.5, "max_tokens": 1500}
        )
        
        assert request.user_idea == "How can we predict student success?"
        assert request.domain == ResearchDomain.LEARNING_ANALYTICS
        assert request.max_results == 15
        assert request.search_weights["dense"] == 0.8
        assert request.search_weights["sparse"] == 0.2
        assert request.generation_params["temperature"] == 0.5
    
    def test_default_values(self):
        """Test default values in hypothesis request."""
        request = HypothesisRequest(
            user_idea="Test idea"
        )
        
        assert request.domain == ResearchDomain.OTHER
        assert request.max_results == 10
        assert request.search_weights["dense"] == 0.7
        assert request.search_weights["sparse"] == 0.3
        assert request.generation_params["temperature"] == 0.7


class TestSearchResult:
    """Test SearchResult model."""
    
    def test_create_search_result(self):
        """Test creating a search result."""
        result = SearchResult(
            document_id="doc_123",
            score=0.85,
            content="This is the content of the document",
            metadata={"keywords": ["test", "example"], "confidence": 0.9},
            source_type="problem"
        )
        
        assert result.document_id == "doc_123"
        assert result.score == 0.85
        assert result.content == "This is the content of the document"
        assert result.metadata["keywords"] == ["test", "example"]
        assert result.source_type == "problem"


class TestHypothesisResponse:
    """Test HypothesisResponse model."""
    
    def test_create_hypothesis_response(self):
        """Test creating a hypothesis response."""
        search_result = SearchResult(
            document_id="doc_1",
            score=0.9,
            content="Test content",
            source_type="problem"
        )
        
        response = HypothesisResponse(
            request_id="req_123",
            user_idea="Test idea",
            search_results=[search_result],
            generated_hypotheses=["Hypothesis 1", "Hypothesis 2"],
            reasoning="This is the reasoning",
            follow_up_questions=["Question 1", "Question 2"],
            suggested_searches=["Search 1", "Search 2"],
            confidence_score=0.8
        )
        
        assert response.request_id == "req_123"
        assert response.user_idea == "Test idea"
        assert len(response.search_results) == 1
        assert len(response.generated_hypotheses) == 2
        assert response.reasoning == "This is the reasoning"
        assert len(response.follow_up_questions) == 2
        assert len(response.suggested_searches) == 2
        assert response.confidence_score == 0.8
        assert isinstance(response.generated_at, datetime)
