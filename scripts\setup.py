#!/usr/bin/env python3
"""
Setup script for the paper hypothesis generator.
"""

import os
import sys
import subprocess
from pathlib import Path


def run_command(command, description):
    """Run a command and handle errors."""
    print(f"Running: {description}")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✓ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ {description} failed:")
        print(f"  Command: {command}")
        print(f"  Error: {e.stderr}")
        return False


def check_python_version():
    """Check if Python version is compatible."""
    if sys.version_info < (3, 8):
        print("Error: Python 3.8 or higher is required")
        return False
    print(f"✓ Python {sys.version_info.major}.{sys.version_info.minor} detected")
    return True


def install_dependencies():
    """Install Python dependencies."""
    commands = [
        ("pip install --upgrade pip", "Upgrading pip"),
        ("pip install -r requirements.txt", "Installing dependencies"),
    ]
    
    for command, description in commands:
        if not run_command(command, description):
            return False
    return True


def download_nltk_data():
    """Download required NLTK data."""
    print("Downloading NLTK data...")
    try:
        import nltk
        nltk.download('punkt', quiet=True)
        nltk.download('stopwords', quiet=True)
        print("✓ NLTK data downloaded")
        return True
    except Exception as e:
        print(f"✗ NLTK data download failed: {e}")
        return False


def create_directories():
    """Create necessary directories."""
    directories = [
        "data/pdfs",
        "data/processed", 
        "data/indices",
        "logs",
        "tests",
        "docs",
        "examples"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"✓ Created directory: {directory}")
    
    return True


def setup_environment():
    """Setup environment file."""
    env_example = Path(".env.example")
    env_file = Path(".env")
    
    if env_example.exists() and not env_file.exists():
        import shutil
        shutil.copy(env_example, env_file)
        print("✓ Created .env file from .env.example")
        print("⚠️  Please edit .env file with your API keys and configuration")
    elif env_file.exists():
        print("✓ .env file already exists")
    else:
        print("⚠️  No .env.example file found")
    
    return True


def generate_sample_data():
    """Generate sample data for testing."""
    try:
        from examples.sample_data_generator import save_sample_data
        save_sample_data()
        print("✓ Sample data generated")
        return True
    except Exception as e:
        print(f"⚠️  Sample data generation failed: {e}")
        return False


def test_installation():
    """Test if installation is working."""
    try:
        # Test imports
        import paper_hgen
        from paper_hgen.models import LiteratureDocument
        from paper_hgen.semantic_space import SemanticSpaceManager
        print("✓ Package imports successful")
        
        # Test CLI
        result = subprocess.run([sys.executable, "-m", "paper_hgen.cli", "--help"], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print("✓ CLI interface working")
        else:
            print("⚠️  CLI interface test failed")
        
        return True
    except Exception as e:
        print(f"✗ Installation test failed: {e}")
        return False


def main():
    """Main setup function."""
    print("🔬 Paper Hypothesis Generator Setup")
    print("=" * 50)
    
    steps = [
        ("Checking Python version", check_python_version),
        ("Creating directories", create_directories),
        ("Installing dependencies", install_dependencies),
        ("Downloading NLTK data", download_nltk_data),
        ("Setting up environment", setup_environment),
        ("Generating sample data", generate_sample_data),
        ("Testing installation", test_installation),
    ]
    
    failed_steps = []
    
    for step_name, step_func in steps:
        print(f"\n{step_name}...")
        if not step_func():
            failed_steps.append(step_name)
    
    print("\n" + "=" * 50)
    if failed_steps:
        print("❌ Setup completed with errors:")
        for step in failed_steps:
            print(f"  - {step}")
        print("\nPlease resolve the errors and run setup again.")
        return 1
    else:
        print("✅ Setup completed successfully!")
        print("\nNext steps:")
        print("1. Edit .env file with your OpenAI API key")
        print("2. Add PDF files to data/pdfs/ directory")
        print("3. Run: python -m paper_hgen.cli extract")
        print("4. Run: python -m paper_hgen.cli build-index")
        print("5. Run: python -m paper_hgen.cli generate")
        print("\nOr try the demo: python -m paper_hgen.cli demo")
        print("Or start the web app: streamlit run paper_hgen/web_app.py")
        return 0


if __name__ == "__main__":
    sys.exit(main())
