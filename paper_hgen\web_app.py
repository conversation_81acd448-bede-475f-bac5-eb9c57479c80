"""
Streamlit web application for the paper hypothesis generator.
"""

import os
import json
import logging
from pathlib import Path
import streamlit as st
from dotenv import load_dotenv
from openai import OpenAI

from .models import HypothesisRequest, ResearchDomain
from .semantic_space import SemanticSpaceManager
from .retrieval import RetrievalEngine
from .reasoning import HypothesisGenerator

# Load environment variables
load_dotenv()

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@st.cache_resource
def initialize_system():
    """Initialize the hypothesis generation system."""
    try:
        # Initialize OpenAI client
        openai_client = OpenAI(api_key=os.getenv('OPENAI_API_KEY'))
        
        # Initialize semantic manager
        semantic_manager = SemanticSpaceManager()
        
        # Load indices if they exist
        index_path = Path('data/indices')
        if index_path.exists():
            semantic_manager.load_all(str(index_path))
        else:
            # Use sample data for demo
            from examples.sample_data_generator import generate_sample_papers
            papers = generate_sample_papers()
            semantic_manager.build_from_literature(papers)
        
        # Initialize retrieval and reasoning
        retrieval_engine = RetrievalEngine(semantic_manager)
        hypothesis_generator = HypothesisGenerator(openai_client, retrieval_engine)
        
        return hypothesis_generator, retrieval_engine
        
    except Exception as e:
        st.error(f"Failed to initialize system: {e}")
        return None, None


def main():
    """Main Streamlit application."""
    st.set_page_config(
        page_title="Paper Hypothesis Generator",
        page_icon="🔬",
        layout="wide"
    )
    
    st.title("🔬 Paper Hypothesis Generator")
    st.markdown("Generate research hypotheses based on literature analysis in learning analytics")
    
    # Initialize system
    hypothesis_generator, retrieval_engine = initialize_system()
    
    if hypothesis_generator is None:
        st.error("System initialization failed. Please check your configuration.")
        return
    
    # Sidebar configuration
    st.sidebar.header("Configuration")
    
    max_results = st.sidebar.slider("Max Search Results", 5, 20, 10)
    dense_weight = st.sidebar.slider("Dense Search Weight", 0.0, 1.0, 0.7, 0.1)
    sparse_weight = 1.0 - dense_weight
    temperature = st.sidebar.slider("Generation Temperature", 0.1, 1.0, 0.7, 0.1)
    
    iterative_mode = st.sidebar.checkbox("Iterative Generation")
    if iterative_mode:
        iterations = st.sidebar.slider("Number of Iterations", 1, 5, 3)
    
    # Main interface
    col1, col2 = st.columns([2, 1])
    
    with col1:
        st.header("Research Idea Input")
        user_idea = st.text_area(
            "Enter your research idea or question:",
            placeholder="e.g., How can we use machine learning to predict student engagement in online courses?",
            height=100
        )
        
        domain = st.selectbox(
            "Research Domain:",
            options=[d.value for d in ResearchDomain],
            index=0
        )
        
        generate_button = st.button("Generate Hypotheses", type="primary")
    
    with col2:
        st.header("Quick Examples")
        example_ideas = [
            "How can we predict student dropout in MOOCs?",
            "What factors influence collaborative learning effectiveness?",
            "How can adaptive learning systems personalize content?",
            "What role does social interaction play in online learning?",
            "How can we measure learning engagement automatically?"
        ]
        
        for i, example in enumerate(example_ideas):
            if st.button(f"Example {i+1}", key=f"example_{i}"):
                st.session_state.user_idea = example
                st.experimental_rerun()
    
    # Handle example selection
    if 'user_idea' in st.session_state:
        user_idea = st.session_state.user_idea
        del st.session_state.user_idea
    
    # Generate hypotheses
    if generate_button and user_idea:
        with st.spinner("Generating hypotheses..."):
            try:
                # Create request
                request = HypothesisRequest(
                    user_idea=user_idea,
                    domain=ResearchDomain(domain),
                    max_results=max_results,
                    search_weights={"dense": dense_weight, "sparse": sparse_weight},
                    generation_params={"temperature": temperature, "max_tokens": 2000}
                )
                
                # Generate hypotheses
                if iterative_mode:
                    responses = hypothesis_generator.iterative_generation(request, iterations)
                    
                    # Display iterative results
                    for i, response in enumerate(responses):
                        with st.expander(f"Iteration {i+1}", expanded=(i == len(responses) - 1)):
                            display_response(response)
                else:
                    response = hypothesis_generator.generate_hypotheses(request)
                    display_response(response)
                    
            except Exception as e:
                st.error(f"Error generating hypotheses: {e}")
    
    # Search interface
    st.header("Literature Search")
    search_col1, search_col2 = st.columns([3, 1])
    
    with search_col1:
        search_query = st.text_input("Search literature:", placeholder="Enter search terms...")
    
    with search_col2:
        search_type = st.selectbox("Search Type:", ["Both", "Problems", "Methods"])
    
    if st.button("Search") and search_query:
        with st.spinner("Searching literature..."):
            try:
                search_types = {
                    "Both": ["problems", "methods"],
                    "Problems": ["problems"],
                    "Methods": ["methods"]
                }[search_type]
                
                results = retrieval_engine.search(
                    search_query,
                    search_types=search_types,
                    max_results=max_results,
                    dense_weight=dense_weight,
                    sparse_weight=sparse_weight
                )
                
                st.subheader(f"Search Results ({len(results)})")
                for i, result in enumerate(results):
                    with st.expander(f"[{result.source_type.upper()}] {result.document_id} (Score: {result.score:.3f})"):
                        st.write(result.content)
                        if result.metadata.get('keywords'):
                            st.write(f"**Keywords:** {', '.join(result.metadata['keywords'])}")
                        
            except Exception as e:
                st.error(f"Search failed: {e}")


def display_response(response):
    """Display hypothesis generation response."""
    st.subheader("Generated Hypotheses")
    st.write(f"**Confidence Score:** {response.confidence_score:.3f}")
    
    for i, hypothesis in enumerate(response.generated_hypotheses, 1):
        st.write(f"{i}. {hypothesis}")
    
    if response.reasoning:
        with st.expander("Reasoning Process"):
            st.write(response.reasoning)
    
    col1, col2 = st.columns(2)
    
    with col1:
        if response.follow_up_questions:
            st.subheader("Follow-up Questions")
            for i, question in enumerate(response.follow_up_questions, 1):
                st.write(f"{i}. {question}")
    
    with col2:
        if response.suggested_searches:
            st.subheader("Suggested Searches")
            for i, search in enumerate(response.suggested_searches, 1):
                st.write(f"{i}. {search}")
    
    if response.search_results:
        with st.expander(f"Relevant Literature ({len(response.search_results)} results)"):
            for i, result in enumerate(response.search_results, 1):
                st.write(f"**{i}. [{result.source_type.upper()}] {result.document_id}** (Score: {result.score:.3f})")
                st.write(result.content[:200] + "...")
                if result.metadata.get('keywords'):
                    st.write(f"*Keywords: {', '.join(result.metadata['keywords'])}*")
                st.write("---")


if __name__ == "__main__":
    main()
