# 🔬 Paper Hypothesis Generator

一个基于文献分析的AI驱动研究假设生成系统，专注于学习分析领域的研究问题发现和假设提出。

## 📋 项目概述

本项目旨在帮助研究者基于已有文献信息，通过设计合理的信息集成融合方式，使得大语言模型能够利用这些信息进行有理可依、有理有据的假设生成。系统支持与研究者的互动中不断改进信息的过程。

### 🎯 主要特性

- **文献信息提取**: 从PDF文献中自动提取研究问题和研究方法
- **语义空间构建**: 基于Qwen3嵌入模型的稠密空间和BM25稀疏空间
- **双路召回检索**: 融合稠密和稀疏检索的混合搜索引擎
- **智能假设生成**: 基于GPT-4o的研究假设生成和推理
- **迭代式改进**: 支持基于反馈的迭代检索和假设优化
- **多种交互方式**: 命令行界面和Web界面

## 🏗️ 系统架构

```
paper_hgen/
├── models.py          # 数据模型定义
├── extractors.py      # PDF文献信息提取
├── semantic_space.py  # 语义空间构建
├── retrieval.py       # 检索匹配引擎
├── reasoning.py       # 推理生成模块
├── cli.py            # 命令行界面
├── web_app.py        # Web界面
└── config.py         # 配置管理
```

## 🚀 快速开始

### 1. 环境要求

- Python 3.8+
- OpenAI API Key
- 8GB+ RAM (推荐)

### 2. 安装

```bash
# 克隆项目
git clone <repository-url>
cd paper_hgen

# 运行安装脚本
python scripts/setup.py
```

### 3. 配置

编辑 `.env` 文件，添加必要的配置：

```env
# OpenAI API配置
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-4o

# 嵌入模型配置
EMBEDDING_MODEL=BAAI/bge-large-zh-v1.5

# 数据路径
PDF_DATA_PATH=./data/pdfs
PROCESSED_DATA_PATH=./data/processed
INDEX_DATA_PATH=./data/indices
```

### 4. 快速演示

```bash
# 运行演示（使用示例数据）
python -m paper_hgen.cli demo
```

## 📖 使用指南

### 命令行界面

#### 1. 提取文献信息

```bash
# 从PDF文件提取信息
python -m paper_hgen.cli extract --pdf-dir data/pdfs --output-dir data/processed
```

#### 2. 构建语义索引

```bash
# 构建语义空间索引
python -m paper_hgen.cli build-index --data-dir data/processed --index-dir data/indices
```

#### 3. 生成假设

```bash
# 基于想法生成假设
python -m paper_hgen.cli generate --idea "如何预测学生在在线课程中的参与度？"

# 迭代式生成
python -m paper_hgen.cli generate --idea "你的研究想法" --iterative --iterations 3
```

### Web界面

```bash
# 启动Web应用
streamlit run paper_hgen/web_app.py
```

然后在浏览器中访问 `http://localhost:8501`

## 🔧 详细功能

### 文献信息提取

系统支持从PDF文献中提取：

- **研究问题**: 明确的研究问题陈述、假设、研究空白
- **研究方法**: 数据收集方法、分析技术、工具和技术
- **元数据**: 标题、作者、年份、关键词等

### 语义空间

#### 稠密空间 (Dense Space)
- 使用Qwen3嵌入模型 (BAAI/bge-large-zh-v1.5)
- FAISS索引用于高效相似性搜索
- 支持语义相似性匹配

#### 稀疏空间 (Sparse Space)
- 基于BM25算法的关键词匹配
- 支持精确词汇匹配
- 互补稠密搜索的不足

### 检索引擎

- **双路召回**: 同时使用稠密和稀疏检索
- **权重融合**: 可配置的权重组合 (默认: 稠密0.7, 稀疏0.3)
- **结果排序**: 基于综合得分的结果排序
- **迭代检索**: 支持基于反馈的查询优化

### 假设生成

- **上下文感知**: 基于检索到的文献内容
- **多假设生成**: 一次生成3-5个可测试假设
- **推理过程**: 提供详细的推理逻辑
- **后续问题**: 生成深入探索的问题
- **搜索建议**: 推荐额外的搜索方向

## 📊 数据格式

### 研究问题格式

```json
{
  "id": "unique_problem_id",
  "text": "研究问题的具体描述",
  "context": "问题的背景和上下文",
  "keywords": ["关键词1", "关键词2"],
  "paper_id": "来源论文ID",
  "confidence": 0.9,
  "domain": "learning_analytics"
}
```

### 研究方法格式

```json
{
  "id": "unique_method_id",
  "name": "方法名称",
  "description": "方法的详细描述",
  "methodology_type": "quantitative/qualitative/mixed",
  "tools_used": ["工具1", "工具2"],
  "data_sources": ["数据源1", "数据源2"],
  "paper_id": "来源论文ID",
  "confidence": 0.8
}
```

## 🧪 测试

```bash
# 运行所有测试
pytest tests/

# 运行特定测试
pytest tests/test_models.py
pytest tests/test_semantic_space.py

# 生成测试覆盖率报告
pytest --cov=paper_hgen tests/
```

## 📁 项目结构

```
paper_hgen/
├── data/                    # 数据目录
│   ├── pdfs/               # PDF文献文件
│   ├── processed/          # 处理后的数据
│   └── indices/            # 语义索引文件
├── paper_hgen/             # 主要代码
│   ├── __init__.py
│   ├── models.py           # 数据模型
│   ├── extractors.py       # 文献提取
│   ├── semantic_space.py   # 语义空间
│   ├── retrieval.py        # 检索引擎
│   ├── reasoning.py        # 推理生成
│   ├── cli.py             # 命令行界面
│   ├── web_app.py         # Web界面
│   └── config.py          # 配置管理
├── tests/                  # 测试文件
├── scripts/                # 工具脚本
├── examples/               # 示例和演示
├── docs/                   # 文档
├── logs/                   # 日志文件
├── requirements.txt        # 依赖列表
├── pyproject.toml         # 项目配置
└── README.md              # 项目说明
```

## ⚙️ 配置选项

### 搜索配置

```python
# 检索权重配置
DENSE_WEIGHT = 0.7          # 稠密搜索权重
SPARSE_WEIGHT = 0.3         # 稀疏搜索权重
MAX_SEARCH_RESULTS = 10     # 最大搜索结果数

# 生成配置
TEMPERATURE = 0.7           # 生成温度
MAX_TOKENS = 2000          # 最大生成长度
MAX_CONTEXT_LENGTH = 8000   # 最大上下文长度
```

### 模型配置

```python
# 嵌入模型
EMBEDDING_MODEL = "BAAI/bge-large-zh-v1.5"

# 生成模型
OPENAI_MODEL = "gpt-4o"
```

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

- OpenAI GPT-4o 用于假设生成
- BAAI BGE 模型用于中文嵌入
- FAISS 用于高效向量搜索
- BM25 用于稀疏检索
- Streamlit 用于Web界面

## 📞 联系方式

如有问题或建议，请通过以下方式联系：

- 提交 Issue
- 发送邮件至 [<EMAIL>]

---

**注意**: 本项目目前专注于学习分析领域，但架构设计支持扩展到其他研究领域。
