"""
Generate sample literature data for testing the hypothesis generation system.
"""

import json
import uuid
from datetime import datetime
from pathlib import Path
from typing import List

from paper_hgen.models import (
    PaperMetadata,
    ResearchProblem,
    ResearchMethod,
    LiteratureDocument,
    ResearchDomain
)


def generate_sample_papers() -> List[LiteratureDocument]:
    """Generate sample literature documents for testing."""
    
    papers = []
    
    # Paper 1: Learning Analytics in MOOCs
    paper1 = LiteratureDocument(
        metadata=PaperMetadata(
            title="Predictive Analytics for Student Success in Massive Open Online Courses",
            authors=["Zhang, L.", "Wang, M.", "Chen, S."],
            year=2023,
            journal="Computers & Education",
            abstract="This study investigates the use of predictive analytics to identify at-risk students in MOOCs and improve completion rates.",
            keywords=["MOOCs", "predictive analytics", "student success", "dropout prediction"],
            domain=ResearchDomain.LEARNING_ANALYTICS
        ),
        research_problems=[
            ResearchProblem(
                id="mooc_dropout_prediction",
                text="How can we predict student dropout in MOOCs using early engagement indicators?",
                context="High dropout rates in MOOCs (often >90%) make it crucial to identify at-risk students early for intervention.",
                keywords=["dropout prediction", "early warning", "MOOC", "engagement"],
                paper_id="mooc_predictive_analytics_2023",
                confidence=0.95,
                domain=ResearchDomain.LEARNING_ANALYTICS
            ),
            ResearchProblem(
                id="engagement_patterns",
                text="What engagement patterns distinguish successful from unsuccessful MOOC learners?",
                context="Understanding behavioral differences can inform both prediction models and intervention strategies.",
                keywords=["engagement patterns", "learning behavior", "success factors"],
                paper_id="mooc_predictive_analytics_2023",
                confidence=0.88,
                domain=ResearchDomain.LEARNING_ANALYTICS
            )
        ],
        research_methods=[
            ResearchMethod(
                id="random_forest_prediction",
                name="Random Forest Classification",
                description="Used random forest algorithm to predict student dropout based on clickstream data, assignment submissions, and forum participation.",
                methodology_type="quantitative",
                tools_used=["Python", "scikit-learn", "pandas"],
                data_sources=["clickstream logs", "assignment data", "forum posts"],
                paper_id="mooc_predictive_analytics_2023",
                confidence=0.92,
                domain=ResearchDomain.LEARNING_ANALYTICS
            ),
            ResearchMethod(
                id="feature_engineering",
                name="Temporal Feature Engineering",
                description="Created time-based features from student activity data including session frequency, duration patterns, and engagement trends.",
                methodology_type="quantitative",
                tools_used=["Python", "feature-engine", "numpy"],
                data_sources=["activity logs", "temporal data"],
                paper_id="mooc_predictive_analytics_2023",
                confidence=0.85,
                domain=ResearchDomain.LEARNING_ANALYTICS
            )
        ]
    )
    papers.append(paper1)
    
    # Paper 2: Adaptive Learning Systems
    paper2 = LiteratureDocument(
        metadata=PaperMetadata(
            title="Personalized Learning Paths in Adaptive Educational Systems: A Deep Learning Approach",
            authors=["Johnson, R.", "Smith, K.", "Brown, A."],
            year=2023,
            journal="Journal of Educational Technology & Society",
            abstract="This research proposes a deep learning framework for generating personalized learning paths in adaptive educational systems.",
            keywords=["adaptive learning", "personalization", "deep learning", "learning paths"],
            domain=ResearchDomain.LEARNING_ANALYTICS
        ),
        research_problems=[
            ResearchProblem(
                id="personalized_paths",
                text="How can deep learning models generate optimal personalized learning paths for individual students?",
                context="Traditional adaptive systems use rule-based approaches that may not capture complex learning patterns.",
                keywords=["personalization", "learning paths", "optimization", "individual differences"],
                paper_id="adaptive_learning_dl_2023",
                confidence=0.93,
                domain=ResearchDomain.LEARNING_ANALYTICS
            ),
            ResearchProblem(
                id="knowledge_state_modeling",
                text="What is the most effective way to model dynamic knowledge states in adaptive learning environments?",
                context="Accurate knowledge state modeling is crucial for effective personalization and path recommendation.",
                keywords=["knowledge modeling", "cognitive state", "dynamic assessment"],
                paper_id="adaptive_learning_dl_2023",
                confidence=0.87,
                domain=ResearchDomain.LEARNING_ANALYTICS
            )
        ],
        research_methods=[
            ResearchMethod(
                id="lstm_knowledge_tracing",
                name="LSTM-based Knowledge Tracing",
                description="Implemented Long Short-Term Memory networks to track student knowledge states over time based on response patterns.",
                methodology_type="quantitative",
                tools_used=["TensorFlow", "Keras", "Python"],
                data_sources=["student responses", "exercise data", "performance history"],
                paper_id="adaptive_learning_dl_2023",
                confidence=0.90,
                domain=ResearchDomain.LEARNING_ANALYTICS
            ),
            ResearchMethod(
                id="reinforcement_learning_paths",
                name="Reinforcement Learning for Path Optimization",
                description="Applied Q-learning algorithm to optimize learning path selection based on student progress and engagement.",
                methodology_type="quantitative",
                tools_used=["OpenAI Gym", "stable-baselines3", "Python"],
                data_sources=["learning outcomes", "engagement metrics", "path completion data"],
                paper_id="adaptive_learning_dl_2023",
                confidence=0.88,
                domain=ResearchDomain.LEARNING_ANALYTICS
            )
        ]
    )
    papers.append(paper2)
    
    # Paper 3: Collaborative Learning Analytics
    paper3 = LiteratureDocument(
        metadata=PaperMetadata(
            title="Social Network Analysis in Collaborative Learning: Understanding Peer Interaction Patterns",
            authors=["Liu, X.", "Garcia, M.", "Thompson, J."],
            year=2022,
            journal="Computers in Human Behavior",
            abstract="This study applies social network analysis to understand how peer interactions affect learning outcomes in collaborative online environments.",
            keywords=["social network analysis", "collaborative learning", "peer interaction", "online learning"],
            domain=ResearchDomain.LEARNING_ANALYTICS
        ),
        research_problems=[
            ResearchProblem(
                id="peer_interaction_effects",
                text="How do different types of peer interactions in online collaborative learning affect individual learning outcomes?",
                context="Understanding the relationship between social interactions and learning can inform the design of collaborative learning environments.",
                keywords=["peer interaction", "collaborative learning", "learning outcomes", "social effects"],
                paper_id="social_network_collab_2022",
                confidence=0.91,
                domain=ResearchDomain.LEARNING_ANALYTICS
            )
        ],
        research_methods=[
            ResearchMethod(
                id="social_network_analysis",
                name="Social Network Analysis",
                description="Applied centrality measures and community detection algorithms to analyze peer interaction networks in online forums.",
                methodology_type="quantitative",
                tools_used=["NetworkX", "Gephi", "R"],
                data_sources=["forum interactions", "collaboration logs", "communication data"],
                paper_id="social_network_collab_2022",
                confidence=0.89,
                domain=ResearchDomain.LEARNING_ANALYTICS
            )
        ]
    )
    papers.append(paper3)
    
    return papers


def save_sample_data(output_dir: str = "data/processed"):
    """Save sample data to JSON files."""
    papers = generate_sample_papers()
    
    output_path = Path(output_dir)
    output_path.mkdir(parents=True, exist_ok=True)
    
    # Save individual papers
    for i, paper in enumerate(papers):
        filename = f"sample_paper_{i+1}.json"
        with open(output_path / filename, 'w', encoding='utf-8') as f:
            json.dump(paper.dict(), f, indent=2, ensure_ascii=False, default=str)
    
    # Save combined dataset
    combined_data = {
        "papers": [paper.dict() for paper in papers],
        "generated_at": datetime.now().isoformat(),
        "total_papers": len(papers)
    }
    
    with open(output_path / "sample_dataset.json", 'w', encoding='utf-8') as f:
        json.dump(combined_data, f, indent=2, ensure_ascii=False, default=str)
    
    print(f"Generated {len(papers)} sample papers in {output_path}")
    return papers


if __name__ == "__main__":
    save_sample_data()
