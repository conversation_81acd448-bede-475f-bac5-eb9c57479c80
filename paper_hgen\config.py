"""
Configuration management for the paper hypothesis generator.
"""

import os
from typing import Dict, Any, Optional
from pathlib import Path
from pydantic import BaseSettings, Field
from dotenv import load_dotenv

# Load environment variables
load_dotenv()


class DatabaseConfig(BaseSettings):
    """Database configuration."""
    host: str = Field(default="localhost", env="DB_HOST")
    port: int = Field(default=5432, env="DB_PORT")
    name: str = Field(default="paper_hgen", env="DB_NAME")
    user: str = Field(default="postgres", env="DB_USER")
    password: str = Field(default="", env="DB_PASSWORD")
    
    @property
    def url(self) -> str:
        return f"postgresql://{self.user}:{self.password}@{self.host}:{self.port}/{self.name}"


class OpenAIConfig(BaseSettings):
    """OpenAI API configuration."""
    api_key: str = Field(..., env="OPENAI_API_KEY")
    model: str = Field(default="gpt-4o", env="OPENAI_MODEL")
    max_tokens: int = Field(default=2000, env="OPENAI_MAX_TOKENS")
    temperature: float = Field(default=0.7, env="OPENAI_TEMPERATURE")


class EmbeddingConfig(BaseSettings):
    """Embedding model configuration."""
    model_name: str = Field(default="BAAI/bge-large-zh-v1.5", env="EMBEDDING_MODEL")
    batch_size: int = Field(default=32, env="EMBEDDING_BATCH_SIZE")
    max_length: int = Field(default=512, env="EMBEDDING_MAX_LENGTH")


class SearchConfig(BaseSettings):
    """Search and retrieval configuration."""
    max_results: int = Field(default=10, env="MAX_SEARCH_RESULTS")
    dense_weight: float = Field(default=0.7, env="DENSE_WEIGHT")
    sparse_weight: float = Field(default=0.3, env="SPARSE_WEIGHT")
    min_score_threshold: float = Field(default=0.1, env="MIN_SCORE_THRESHOLD")
    normalize_scores: bool = Field(default=True, env="NORMALIZE_SCORES")


class PathConfig(BaseSettings):
    """Path configuration."""
    pdf_data_path: Path = Field(default=Path("./data/pdfs"), env="PDF_DATA_PATH")
    processed_data_path: Path = Field(default=Path("./data/processed"), env="PROCESSED_DATA_PATH")
    index_data_path: Path = Field(default=Path("./data/indices"), env="INDEX_DATA_PATH")
    log_file: Path = Field(default=Path("./logs/paper_hgen.log"), env="LOG_FILE")
    
    def __post_init__(self):
        """Create directories if they don't exist."""
        for path in [self.pdf_data_path, self.processed_data_path, self.index_data_path]:
            path.mkdir(parents=True, exist_ok=True)
        
        # Create log directory
        self.log_file.parent.mkdir(parents=True, exist_ok=True)


class LoggingConfig(BaseSettings):
    """Logging configuration."""
    level: str = Field(default="INFO", env="LOG_LEVEL")
    format: str = Field(
        default="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        env="LOG_FORMAT"
    )
    file_enabled: bool = Field(default=True, env="LOG_FILE_ENABLED")
    console_enabled: bool = Field(default=True, env="LOG_CONSOLE_ENABLED")


class AppConfig(BaseSettings):
    """Main application configuration."""
    # Sub-configurations
    openai: OpenAIConfig = OpenAIConfig()
    embedding: EmbeddingConfig = EmbeddingConfig()
    search: SearchConfig = SearchConfig()
    paths: PathConfig = PathConfig()
    logging: LoggingConfig = LoggingConfig()
    database: Optional[DatabaseConfig] = None
    
    # General settings
    debug: bool = Field(default=False, env="DEBUG")
    max_context_length: int = Field(default=8000, env="MAX_CONTEXT_LENGTH")
    
    # Web app settings
    web_host: str = Field(default="localhost", env="WEB_HOST")
    web_port: int = Field(default=8501, env="WEB_PORT")
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        # Initialize paths
        self.paths.__post_init__()
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary."""
        return {
            "openai": self.openai.dict(),
            "embedding": self.embedding.dict(),
            "search": self.search.dict(),
            "paths": {k: str(v) for k, v in self.paths.dict().items()},
            "logging": self.logging.dict(),
            "debug": self.debug,
            "max_context_length": self.max_context_length,
            "web_host": self.web_host,
            "web_port": self.web_port,
        }
    
    @classmethod
    def from_file(cls, config_path: str) -> "AppConfig":
        """Load configuration from file."""
        import yaml
        
        with open(config_path, 'r', encoding='utf-8') as f:
            config_data = yaml.safe_load(f)
        
        return cls(**config_data)
    
    def save_to_file(self, config_path: str) -> None:
        """Save configuration to file."""
        import yaml
        
        with open(config_path, 'w', encoding='utf-8') as f:
            yaml.dump(self.to_dict(), f, default_flow_style=False, allow_unicode=True)


# Global configuration instance
config = AppConfig()


def get_config() -> AppConfig:
    """Get the global configuration instance."""
    return config


def setup_logging(config: AppConfig) -> None:
    """Setup logging based on configuration."""
    import logging
    
    # Create logger
    logger = logging.getLogger()
    logger.setLevel(getattr(logging, config.logging.level))
    
    # Clear existing handlers
    logger.handlers.clear()
    
    # Create formatter
    formatter = logging.Formatter(config.logging.format)
    
    # Console handler
    if config.logging.console_enabled:
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)
    
    # File handler
    if config.logging.file_enabled:
        file_handler = logging.FileHandler(config.paths.log_file, encoding='utf-8')
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)


def validate_config(config: AppConfig) -> bool:
    """Validate configuration settings."""
    errors = []
    
    # Check OpenAI API key
    if not config.openai.api_key:
        errors.append("OpenAI API key is required")
    
    # Check weights sum to 1
    if abs(config.search.dense_weight + config.search.sparse_weight - 1.0) > 0.01:
        errors.append("Dense and sparse weights must sum to 1.0")
    
    # Check paths exist or can be created
    try:
        config.paths.__post_init__()
    except Exception as e:
        errors.append(f"Path configuration error: {e}")
    
    if errors:
        for error in errors:
            print(f"Configuration error: {error}")
        return False
    
    return True


# Initialize logging on import
setup_logging(config)
