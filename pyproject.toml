[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "paper-hypothesis-generator"
version = "0.1.0"
description = "AI-powered research hypothesis generation based on literature analysis"
authors = [{name = "Research Team", email = "<EMAIL>"}]
license = {text = "MIT"}
readme = "README.md"
requires-python = ">=3.8"
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Science/Research",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
]
dependencies = [
    "torch>=2.0.0",
    "transformers>=4.30.0",
    "sentence-transformers>=2.2.0",
    "openai>=1.0.0",
    "numpy>=1.24.0",
    "pandas>=2.0.0",
    "scikit-learn>=1.3.0",
    "PyPDF2>=3.0.0",
    "pdfplumber>=0.9.0",
    "faiss-cpu>=1.7.4",
    "rank-bm25>=0.2.2",
    "tqdm>=4.65.0",
    "nltk>=3.8.0",
    "spacy>=3.6.0",
    "pydantic>=2.0.0",
    "python-dotenv>=1.0.0",
    "pyyaml>=6.0.0",
    "click>=8.1.0",
]

[project.optional-dependencies]
web = ["fastapi>=0.100.0", "uvicorn>=0.23.0", "streamlit>=1.25.0"]
dev = ["pytest>=7.4.0", "pytest-asyncio>=0.21.0", "black>=23.0.0", "flake8>=6.0.0"]

[project.scripts]
paper-hgen = "paper_hgen.cli:main"

[tool.setuptools.packages.find]
where = ["."]
include = ["paper_hgen*"]

[tool.black]
line-length = 88
target-version = ['py38']

[tool.flake8]
max-line-length = 88
extend-ignore = ["E203", "W503"]
