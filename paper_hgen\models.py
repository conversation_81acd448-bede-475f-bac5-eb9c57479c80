"""
Data models for the paper hypothesis generation system.
"""

from typing import List, Optional, Dict, Any, Union
from datetime import datetime
from pydantic import BaseModel, Field
from enum import Enum


class ResearchDomain(str, Enum):
    """Research domains supported by the system."""
    LEARNING_ANALYTICS = "learning_analytics"
    EDUCATION = "education"
    COMPUTER_SCIENCE = "computer_science"
    PSYCHOLOGY = "psychology"
    OTHER = "other"


class PaperMetadata(BaseModel):
    """Metadata for a research paper."""
    title: str
    authors: List[str]
    year: Optional[int] = None
    journal: Optional[str] = None
    doi: Optional[str] = None
    abstract: Optional[str] = None
    keywords: List[str] = Field(default_factory=list)
    domain: ResearchDomain = ResearchDomain.OTHER
    file_path: Optional[str] = None
    extracted_at: datetime = Field(default_factory=datetime.now)


class ResearchProblem(BaseModel):
    """A research problem or question extracted from literature."""
    id: str = Field(..., description="Unique identifier for the research problem")
    text: str = Field(..., description="The research problem statement")
    context: Optional[str] = Field(None, description="Additional context around the problem")
    domain: ResearchDomain = ResearchDomain.OTHER
    keywords: List[str] = Field(default_factory=list)
    paper_id: str = Field(..., description="ID of the source paper")
    confidence: float = Field(0.0, ge=0.0, le=1.0, description="Extraction confidence score")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class ResearchMethod(BaseModel):
    """A research method or approach extracted from literature."""
    id: str = Field(..., description="Unique identifier for the research method")
    name: str = Field(..., description="Name of the research method")
    description: str = Field(..., description="Description of the method")
    methodology_type: str = Field(..., description="Type of methodology (quantitative, qualitative, mixed)")
    tools_used: List[str] = Field(default_factory=list, description="Tools or techniques used")
    data_sources: List[str] = Field(default_factory=list, description="Types of data sources")
    domain: ResearchDomain = ResearchDomain.OTHER
    paper_id: str = Field(..., description="ID of the source paper")
    confidence: float = Field(0.0, ge=0.0, le=1.0, description="Extraction confidence score")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class LiteratureDocument(BaseModel):
    """Complete document representing extracted information from a paper."""
    metadata: PaperMetadata
    research_problems: List[ResearchProblem] = Field(default_factory=list)
    research_methods: List[ResearchMethod] = Field(default_factory=list)
    full_text: Optional[str] = None
    sections: Dict[str, str] = Field(default_factory=dict)  # section_name -> content
    
    @property
    def paper_id(self) -> str:
        """Generate a unique paper ID based on metadata."""
        return f"{self.metadata.title[:50].replace(' ', '_')}_{self.metadata.year or 'unknown'}"


class HypothesisRequest(BaseModel):
    """Request for hypothesis generation."""
    user_idea: str = Field(..., description="User's initial research idea or question")
    domain: ResearchDomain = ResearchDomain.OTHER
    max_results: int = Field(10, ge=1, le=50, description="Maximum number of search results")
    search_weights: Dict[str, float] = Field(
        default_factory=lambda: {"dense": 0.7, "sparse": 0.3},
        description="Weights for dense and sparse search"
    )
    generation_params: Dict[str, Any] = Field(
        default_factory=lambda: {"temperature": 0.7, "max_tokens": 2000},
        description="Parameters for hypothesis generation"
    )


class SearchResult(BaseModel):
    """A single search result from the retrieval engine."""
    document_id: str
    score: float
    content: str
    metadata: Dict[str, Any] = Field(default_factory=dict)
    source_type: str  # "problem", "method", "full_text"


class HypothesisResponse(BaseModel):
    """Response containing generated hypotheses."""
    request_id: str = Field(..., description="Unique identifier for the request")
    user_idea: str = Field(..., description="Original user idea")
    search_results: List[SearchResult] = Field(default_factory=list)
    generated_hypotheses: List[str] = Field(default_factory=list)
    reasoning: str = Field("", description="AI reasoning process")
    follow_up_questions: List[str] = Field(default_factory=list)
    suggested_searches: List[str] = Field(default_factory=list)
    confidence_score: float = Field(0.0, ge=0.0, le=1.0)
    generated_at: datetime = Field(default_factory=datetime.now)
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }
