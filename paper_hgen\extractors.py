"""
Literature extraction modules for processing PDF papers and extracting
research problems and methods.
"""

import re
import json
import logging
from typing import List, Dict, Optional, Tuple
from pathlib import Path

try:
    import PyPDF2
    PYPDF2_AVAILABLE = True
except ImportError:
    PYPDF2_AVAILABLE = False

try:
    import pdfplumber
    PDFPLUMBER_AVAILABLE = True
except ImportError:
    PDFPLUMBER_AVAILABLE = False

try:
    from openai import OpenAI
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False

from .models import (
    PaperMetadata, 
    ResearchProblem, 
    ResearchMethod, 
    LiteratureDocument,
    ResearchDomain
)

logger = logging.getLogger(__name__)


class PDFExtractor:
    """Extract text content from PDF files."""
    
    def __init__(self):
        self.supported_formats = ['.pdf']
    
    def extract_text(self, pdf_path: str) -> str:
        """Extract text from PDF using multiple methods for robustness."""
        if not PDFPLUMBER_AVAILABLE and not <PERSON>YPDF2_AVAILABLE:
            raise ImportError("Neither pdfplumber nor PyPDF2 is available. Please install one of them.")

        try:
            # Try pdfplumber first (better for complex layouts)
            if PDFPLUMBER_AVAILABLE:
                return self._extract_with_pdfplumber(pdf_path)
        except Exception as e:
            logger.warning(f"pdfplumber failed for {pdf_path}: {e}")

        try:
            # Fallback to PyPDF2
            if PYPDF2_AVAILABLE:
                return self._extract_with_pypdf2(pdf_path)
        except Exception as e2:
            logger.error(f"PyPDF2 failed for {pdf_path}: {e2}")

        return ""
    
    def _extract_with_pdfplumber(self, pdf_path: str) -> str:
        """Extract text using pdfplumber."""
        if not PDFPLUMBER_AVAILABLE:
            raise ImportError("pdfplumber is not available")

        text = ""
        with pdfplumber.open(pdf_path) as pdf:
            for page in pdf.pages:
                page_text = page.extract_text()
                if page_text:
                    text += page_text + "\n"
        return text
    
    def _extract_with_pypdf2(self, pdf_path: str) -> str:
        """Extract text using PyPDF2."""
        if not PYPDF2_AVAILABLE:
            raise ImportError("PyPDF2 is not available")

        text = ""
        with open(pdf_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            for page in pdf_reader.pages:
                text += page.extract_text() + "\n"
        return text
    
    def extract_sections(self, text: str) -> Dict[str, str]:
        """Extract common paper sections from text."""
        sections = {}
        
        # Common section patterns
        section_patterns = {
            'abstract': r'(?i)abstract\s*\n(.*?)(?=\n\s*(?:keywords|introduction|1\.|i\.))',
            'introduction': r'(?i)(?:1\.|i\.)\s*introduction\s*\n(.*?)(?=\n\s*(?:2\.|ii\.|related work|literature review))',
            'methodology': r'(?i)(?:methodology|methods|approach)\s*\n(.*?)(?=\n\s*(?:results|findings|evaluation))',
            'results': r'(?i)(?:results|findings|evaluation)\s*\n(.*?)(?=\n\s*(?:discussion|conclusion))',
            'conclusion': r'(?i)conclusion\s*\n(.*?)(?=\n\s*(?:references|bibliography))',
        }
        
        for section_name, pattern in section_patterns.items():
            match = re.search(pattern, text, re.DOTALL | re.MULTILINE)
            if match:
                sections[section_name] = match.group(1).strip()
        
        return sections


class LiteratureExtractor:
    """Extract research problems and methods from literature using LLM."""
    
    def __init__(self, openai_client=None, model: str = "gpt-4o"):
        if openai_client is None and not OPENAI_AVAILABLE:
            raise ImportError("OpenAI client is required but openai package is not available")
        self.client = openai_client
        self.model = model
        self.pdf_extractor = PDFExtractor()
    
    def extract_from_pdf(self, pdf_path: str) -> LiteratureDocument:
        """Extract complete literature information from a PDF file."""
        # Extract text and metadata
        text = self.pdf_extractor.extract_text(pdf_path)
        sections = self.pdf_extractor.extract_sections(text)
        metadata = self._extract_metadata(text, pdf_path)
        
        # Extract research problems and methods using LLM
        research_problems = self._extract_research_problems(text, metadata.title)
        research_methods = self._extract_research_methods(text, metadata.title)
        
        return LiteratureDocument(
            metadata=metadata,
            research_problems=research_problems,
            research_methods=research_methods,
            full_text=text,
            sections=sections
        )
    
    def _extract_metadata(self, text: str, file_path: str) -> PaperMetadata:
        """Extract paper metadata from text."""
        # Simple regex-based extraction (can be improved with LLM)
        title_match = re.search(r'^(.+?)(?:\n|$)', text.strip())
        title = title_match.group(1).strip() if title_match else Path(file_path).stem
        
        # Extract year
        year_match = re.search(r'\b(19|20)\d{2}\b', text)
        year = int(year_match.group()) if year_match else None
        
        # Extract authors (simplified)
        authors = []
        author_patterns = [
            r'(?:Authors?|By):\s*([^\n]+)',
            r'^([A-Z][a-z]+ [A-Z][a-z]+(?:,\s*[A-Z][a-z]+ [A-Z][a-z]+)*)',
        ]
        for pattern in author_patterns:
            match = re.search(pattern, text, re.MULTILINE)
            if match:
                authors = [name.strip() for name in match.group(1).split(',')]
                break
        
        return PaperMetadata(
            title=title,
            authors=authors,
            year=year,
            file_path=file_path,
            domain=ResearchDomain.LEARNING_ANALYTICS  # Default for this project
        )

    def _extract_research_problems(self, text: str, title: str) -> List[ResearchProblem]:
        """Extract research problems using LLM."""
        prompt = f"""
        Please analyze the following research paper and extract the main research problems or questions.

        Paper Title: {title}

        Paper Content (first 4000 chars):
        {text[:4000]}

        Extract research problems in the following JSON format:
        {{
            "research_problems": [
                {{
                    "text": "The main research problem statement",
                    "context": "Additional context or background",
                    "keywords": ["keyword1", "keyword2"],
                    "confidence": 0.9
                }}
            ]
        }}

        Focus on:
        1. Explicit research questions stated in the paper
        2. Problems the authors aim to solve
        3. Gaps in existing research they address
        4. Hypotheses they propose to test

        Return only valid JSON.
        """

        try:
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[{"role": "user", "content": prompt}],
                temperature=0.3,
                max_tokens=1500
            )

            result = json.loads(response.choices[0].message.content)
            problems = []

            for i, problem_data in enumerate(result.get("research_problems", [])):
                problem = ResearchProblem(
                    id=f"{title[:30].replace(' ', '_')}_{i}",
                    text=problem_data["text"],
                    context=problem_data.get("context", ""),
                    keywords=problem_data.get("keywords", []),
                    paper_id=title,
                    confidence=problem_data.get("confidence", 0.5),
                    domain=ResearchDomain.LEARNING_ANALYTICS
                )
                problems.append(problem)

            return problems

        except Exception as e:
            logger.error(f"Failed to extract research problems: {e}")
            return []

    def _extract_research_methods(self, text: str, title: str) -> List[ResearchMethod]:
        """Extract research methods using LLM."""
        prompt = f"""
        Please analyze the following research paper and extract the research methods and approaches used.

        Paper Title: {title}

        Paper Content (first 4000 chars):
        {text[:4000]}

        Extract research methods in the following JSON format:
        {{
            "research_methods": [
                {{
                    "name": "Method name",
                    "description": "Detailed description of the method",
                    "methodology_type": "quantitative/qualitative/mixed",
                    "tools_used": ["tool1", "tool2"],
                    "data_sources": ["source1", "source2"],
                    "confidence": 0.9
                }}
            ]
        }}

        Focus on:
        1. Data collection methods
        2. Analysis techniques
        3. Experimental designs
        4. Tools and technologies used
        5. Statistical or qualitative analysis approaches

        Return only valid JSON.
        """

        try:
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[{"role": "user", "content": prompt}],
                temperature=0.3,
                max_tokens=1500
            )

            result = json.loads(response.choices[0].message.content)
            methods = []

            for i, method_data in enumerate(result.get("research_methods", [])):
                method = ResearchMethod(
                    id=f"{title[:30].replace(' ', '_')}_method_{i}",
                    name=method_data["name"],
                    description=method_data["description"],
                    methodology_type=method_data.get("methodology_type", "unknown"),
                    tools_used=method_data.get("tools_used", []),
                    data_sources=method_data.get("data_sources", []),
                    paper_id=title,
                    confidence=method_data.get("confidence", 0.5),
                    domain=ResearchDomain.LEARNING_ANALYTICS
                )
                methods.append(method)

            return methods

        except Exception as e:
            logger.error(f"Failed to extract research methods: {e}")
            return []
