{"metadata": {"title": "Predictive Analytics for Student Success in Massive Open Online Courses", "authors": ["<PERSON>, <PERSON>.", "<PERSON>, <PERSON><PERSON>", "<PERSON>, S."], "year": 2023, "journal": "Computers & Education", "doi": null, "abstract": "This study investigates the use of predictive analytics to identify at-risk students in MOOCs and improve completion rates.", "keywords": ["MOOCs", "predictive analytics", "student success", "dropout prediction"], "domain": "learning_analytics", "file_path": null, "extracted_at": "2025-06-19 15:32:01.194208"}, "research_problems": [{"id": "mooc_dropout_prediction", "text": "How can we predict student dropout in MOOCs using early engagement indicators?", "context": "High dropout rates in MOOCs (often >90%) make it crucial to identify at-risk students early for intervention.", "domain": "learning_analytics", "keywords": ["dropout prediction", "early warning", "MOOC", "engagement"], "paper_id": "mooc_predictive_analytics_2023", "confidence": 0.95}, {"id": "engagement_patterns", "text": "What engagement patterns distinguish successful from unsuccessful MOOC learners?", "context": "Understanding behavioral differences can inform both prediction models and intervention strategies.", "domain": "learning_analytics", "keywords": ["engagement patterns", "learning behavior", "success factors"], "paper_id": "mooc_predictive_analytics_2023", "confidence": 0.88}], "research_methods": [{"id": "random_forest_prediction", "name": "Random Forest Classification", "description": "Used random forest algorithm to predict student dropout based on clickstream data, assignment submissions, and forum participation.", "methodology_type": "quantitative", "tools_used": ["Python", "scikit-learn", "pandas"], "data_sources": ["clickstream logs", "assignment data", "forum posts"], "domain": "learning_analytics", "paper_id": "mooc_predictive_analytics_2023", "confidence": 0.92}, {"id": "feature_engineering", "name": "Temporal Feature Engineering", "description": "Created time-based features from student activity data including session frequency, duration patterns, and engagement trends.", "methodology_type": "quantitative", "tools_used": ["Python", "feature-engine", "numpy"], "data_sources": ["activity logs", "temporal data"], "domain": "learning_analytics", "paper_id": "mooc_predictive_analytics_2023", "confidence": 0.85}], "full_text": null, "sections": {}}