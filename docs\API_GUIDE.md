# API 使用指南

本文档介绍如何在代码中使用 Paper Hypothesis Generator 的各个模块。

## 基本使用

### 1. 初始化系统

```python
import os
from openai import OpenAI
from paper_hgen.semantic_space import SemanticSpaceManager
from paper_hgen.retrieval import RetrievalEngine
from paper_hgen.reasoning import HypothesisGenerator
from paper_hgen.models import HypothesisRequest, ResearchDomain

# 初始化OpenAI客户端
openai_client = OpenAI(api_key=os.getenv('OPENAI_API_KEY'))

# 初始化语义空间管理器
semantic_manager = SemanticSpaceManager()

# 加载已构建的索引
semantic_manager.load_all('data/indices')

# 初始化检索引擎
retrieval_engine = RetrievalEngine(semantic_manager)

# 初始化假设生成器
hypothesis_generator = HypothesisGenerator(openai_client, retrieval_engine)
```

### 2. 文献信息提取

```python
from paper_hgen.extractors import LiteratureExtractor

# 初始化提取器
extractor = LiteratureExtractor(openai_client)

# 从PDF提取信息
literature_doc = extractor.extract_from_pdf('path/to/paper.pdf')

print(f"提取到 {len(literature_doc.research_problems)} 个研究问题")
print(f"提取到 {len(literature_doc.research_methods)} 个研究方法")
```

### 3. 构建语义空间

```python
from paper_hgen.models import LiteratureDocument

# 假设你有一系列文献文档
literature_docs = [...]  # List[LiteratureDocument]

# 构建语义空间
semantic_manager = SemanticSpaceManager(dense_model="BAAI/bge-large-zh-v1.5")
semantic_manager.build_from_literature(literature_docs)

# 保存索引
semantic_manager.save_all('data/indices')
```

### 4. 检索相关文献

```python
from paper_hgen.retrieval import RetrievalConfig

# 配置检索参数
config = RetrievalConfig(
    dense_weight=0.7,
    sparse_weight=0.3,
    max_results=10,
    min_score_threshold=0.1
)

# 搜索相关问题
query = "如何预测学生在在线课程中的参与度？"
results = retrieval_engine.search(
    query, 
    search_types=['problems', 'methods'],
    dense_weight=0.7,
    sparse_weight=0.3,
    max_results=10
)

for result in results:
    print(f"[{result.source_type}] {result.document_id}: {result.score:.3f}")
    print(f"内容: {result.content[:100]}...")
```

### 5. 生成研究假设

```python
# 创建假设生成请求
request = HypothesisRequest(
    user_idea="如何使用机器学习预测学生在在线课程中的参与度？",
    domain=ResearchDomain.LEARNING_ANALYTICS,
    max_results=10,
    search_weights={"dense": 0.7, "sparse": 0.3},
    generation_params={"temperature": 0.7, "max_tokens": 2000}
)

# 生成假设
response = hypothesis_generator.generate_hypotheses(request)

print(f"生成的假设 ({len(response.generated_hypotheses)}):")
for i, hypothesis in enumerate(response.generated_hypotheses, 1):
    print(f"{i}. {hypothesis}")

print(f"\n推理过程:\n{response.reasoning}")
print(f"\n置信度: {response.confidence_score:.3f}")
```

### 6. 迭代式假设生成

```python
# 迭代式生成（3轮）
responses = hypothesis_generator.iterative_generation(request, iterations=3)

for i, response in enumerate(responses):
    print(f"\n=== 第 {i+1} 轮 ===")
    print(f"用户想法: {response.user_idea}")
    print(f"生成假设数: {len(response.generated_hypotheses)}")
    print(f"置信度: {response.confidence_score:.3f}")
```

## 高级用法

### 自定义检索策略

```python
from paper_hgen.retrieval import IterativeRetrieval

# 创建迭代检索器
iterative_retrieval = IterativeRetrieval(retrieval_engine)

# 第一次搜索
initial_results = iterative_retrieval.search_with_context(
    "机器学习在教育中的应用",
    max_results=5
)

# 基于反馈的后续搜索
refined_results = iterative_retrieval.search_with_context(
    "深度学习预测学生表现",
    previous_results=initial_results,
    refinement_feedback="需要更多关于神经网络的方法",
    avoid_duplicates=True
)

# 查看搜索历史
history = iterative_retrieval.get_search_history()
```

### 自定义语义空间

```python
from paper_hgen.semantic_space import DenseSpace, SparseSpace

# 创建自定义稠密空间
dense_space = DenseSpace(model_name="sentence-transformers/all-MiniLM-L6-v2")

# 准备文档数据
documents = [
    {"content": "文档内容1", "metadata": {"id": "doc1"}},
    {"content": "文档内容2", "metadata": {"id": "doc2"}},
]

# 构建索引
dense_space.build_index(documents)

# 搜索
results = dense_space.search("查询文本", top_k=5)
```

### 批量处理PDF文件

```python
from pathlib import Path
import json

def batch_extract_pdfs(pdf_directory, output_directory):
    """批量提取PDF文件信息"""
    extractor = LiteratureExtractor(openai_client)
    
    pdf_files = list(Path(pdf_directory).glob("*.pdf"))
    extracted_docs = []
    
    for pdf_file in pdf_files:
        try:
            doc = extractor.extract_from_pdf(str(pdf_file))
            extracted_docs.append(doc)
            
            # 保存单个文档
            output_file = Path(output_directory) / f"{pdf_file.stem}.json"
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(doc.dict(), f, indent=2, ensure_ascii=False, default=str)
                
        except Exception as e:
            print(f"处理 {pdf_file.name} 时出错: {e}")
    
    return extracted_docs

# 使用示例
docs = batch_extract_pdfs("data/pdfs", "data/processed")
```

### 自定义假设生成提示

```python
class CustomHypothesisGenerator(HypothesisGenerator):
    """自定义假设生成器"""
    
    def _generate_initial_hypotheses(self, user_idea, search_results, generation_params):
        # 自定义提示模板
        custom_prompt = f"""
        作为教育技术专家，基于以下研究想法和文献：
        
        研究想法: {user_idea}
        
        相关文献: {self._prepare_context(search_results)}
        
        请生成3个创新的、可测试的研究假设，要求：
        1. 具有实际应用价值
        2. 基于现有文献但有所创新
        3. 可以通过实验验证
        
        返回JSON格式...
        """
        
        # 调用LLM生成
        # ... 实现细节
        
        return hypotheses, reasoning

# 使用自定义生成器
custom_generator = CustomHypothesisGenerator(openai_client, retrieval_engine)
```

## 错误处理

```python
try:
    response = hypothesis_generator.generate_hypotheses(request)
except Exception as e:
    print(f"假设生成失败: {e}")
    # 处理错误...

# 检查API配置
from paper_hgen.config import validate_config, get_config

config = get_config()
if not validate_config(config):
    print("配置验证失败")
```

## 性能优化

### 1. 批量嵌入

```python
# 对于大量文档，使用批量处理
semantic_manager = SemanticSpaceManager()
semantic_manager.embedding.batch_size = 64  # 增加批量大小
```

### 2. 缓存结果

```python
import functools

@functools.lru_cache(maxsize=100)
def cached_search(query, max_results=10):
    """缓存搜索结果"""
    return retrieval_engine.search(query, max_results=max_results)
```

### 3. 异步处理

```python
import asyncio
from concurrent.futures import ThreadPoolExecutor

async def async_extract_pdfs(pdf_files):
    """异步提取PDF文件"""
    loop = asyncio.get_event_loop()
    
    with ThreadPoolExecutor(max_workers=4) as executor:
        tasks = [
            loop.run_in_executor(executor, extractor.extract_from_pdf, str(pdf_file))
            for pdf_file in pdf_files
        ]
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
    
    return [r for r in results if not isinstance(r, Exception)]
```

## 最佳实践

1. **定期更新索引**: 当添加新文献时，重新构建语义索引
2. **调整权重**: 根据具体领域调整稠密和稀疏搜索的权重
3. **监控性能**: 记录搜索和生成的响应时间
4. **验证结果**: 人工验证生成的假设质量
5. **增量更新**: 对于大型文献库，考虑增量更新策略
