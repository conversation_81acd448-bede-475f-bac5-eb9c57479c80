"""
Command-line interface for the paper hypothesis generator.
"""

import os
import json
import logging
from pathlib import Path
from typing import Optional
import click
from dotenv import load_dotenv
from openai import OpenAI

from .models import HypothesisRequest, ResearchDomain
from .extractors import LiteratureExtractor
from .semantic_space import SemanticSpaceManager
from .retrieval import Retrieval<PERSON><PERSON><PERSON>
from .reasoning import HypothesisGenerator

# Load environment variables
load_dotenv()

# Setup logging
logging.basicConfig(
    level=getattr(logging, os.getenv('LOG_LEVEL', 'INFO')),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


@click.group()
@click.option('--config', '-c', help='Configuration file path')
@click.pass_context
def cli(ctx, config):
    """Paper Hypothesis Generator CLI."""
    ctx.ensure_object(dict)
    ctx.obj['config'] = config


@cli.command()
@click.option('--pdf-dir', '-p', default='data/pdfs', help='Directory containing PDF files')
@click.option('--output-dir', '-o', default='data/processed', help='Output directory for processed data')
@click.option('--force', '-f', is_flag=True, help='Force reprocessing of existing files')
def extract(pdf_dir, output_dir, force):
    """Extract information from PDF literature."""
    click.echo(f"Extracting literature from {pdf_dir}...")
    
    # Initialize OpenAI client
    openai_client = OpenAI(api_key=os.getenv('OPENAI_API_KEY'))
    extractor = LiteratureExtractor(openai_client)
    
    pdf_path = Path(pdf_dir)
    output_path = Path(output_dir)
    output_path.mkdir(parents=True, exist_ok=True)
    
    if not pdf_path.exists():
        click.echo(f"Error: PDF directory {pdf_path} does not exist")
        return
    
    pdf_files = list(pdf_path.glob('*.pdf'))
    if not pdf_files:
        click.echo(f"No PDF files found in {pdf_path}")
        return
    
    click.echo(f"Found {len(pdf_files)} PDF files")
    
    extracted_docs = []
    with click.progressbar(pdf_files, label='Processing PDFs') as bar:
        for pdf_file in bar:
            output_file = output_path / f"{pdf_file.stem}.json"
            
            if output_file.exists() and not force:
                click.echo(f"Skipping {pdf_file.name} (already processed)")
                continue
            
            try:
                doc = extractor.extract_from_pdf(str(pdf_file))
                extracted_docs.append(doc)
                
                # Save individual document
                with open(output_file, 'w', encoding='utf-8') as f:
                    json.dump(doc.dict(), f, indent=2, ensure_ascii=False, default=str)
                
            except Exception as e:
                click.echo(f"Error processing {pdf_file.name}: {e}")
    
    click.echo(f"Extracted information from {len(extracted_docs)} documents")


@cli.command()
@click.option('--data-dir', '-d', default='data/processed', help='Directory containing processed literature data')
@click.option('--index-dir', '-i', default='data/indices', help='Directory to save semantic indices')
@click.option('--model', '-m', default='BAAI/bge-large-zh-v1.5', help='Embedding model name')
def build_index(data_dir, index_dir, model):
    """Build semantic space indices from processed literature."""
    click.echo(f"Building semantic indices from {data_dir}...")
    
    data_path = Path(data_dir)
    index_path = Path(index_dir)
    index_path.mkdir(parents=True, exist_ok=True)
    
    # Load processed documents
    json_files = list(data_path.glob('*.json'))
    if not json_files:
        click.echo(f"No JSON files found in {data_path}")
        return
    
    from .models import LiteratureDocument
    literature_docs = []
    
    with click.progressbar(json_files, label='Loading documents') as bar:
        for json_file in bar:
            try:
                with open(json_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                doc = LiteratureDocument(**data)
                literature_docs.append(doc)
            except Exception as e:
                click.echo(f"Error loading {json_file.name}: {e}")
    
    if not literature_docs:
        click.echo("No valid documents loaded")
        return
    
    # Build semantic spaces
    click.echo(f"Building semantic spaces for {len(literature_docs)} documents...")
    semantic_manager = SemanticSpaceManager(dense_model=model)
    semantic_manager.build_from_literature(literature_docs)
    
    # Save indices
    semantic_manager.save_all(str(index_path))
    click.echo(f"Semantic indices saved to {index_path}")


@cli.command()
@click.option('--idea', '-i', prompt='Research idea', help='Your research idea or question')
@click.option('--index-dir', '-d', default='data/indices', help='Directory containing semantic indices')
@click.option('--max-results', '-n', default=10, help='Maximum number of search results')
@click.option('--iterative', is_flag=True, help='Enable iterative hypothesis generation')
@click.option('--iterations', default=3, help='Number of iterations for iterative generation')
def generate(idea, index_dir, max_results, iterative, iterations):
    """Generate research hypotheses based on your idea."""
    click.echo(f"Generating hypotheses for: {idea}")
    
    # Initialize components
    openai_client = OpenAI(api_key=os.getenv('OPENAI_API_KEY'))
    semantic_manager = SemanticSpaceManager()
    
    # Load semantic indices
    index_path = Path(index_dir)
    if not index_path.exists():
        click.echo(f"Error: Index directory {index_path} does not exist. Run 'build-index' first.")
        return
    
    semantic_manager.load_all(str(index_path))
    
    # Initialize retrieval and reasoning
    retrieval_engine = RetrievalEngine(semantic_manager)
    hypothesis_generator = HypothesisGenerator(openai_client, retrieval_engine)
    
    # Create request
    request = HypothesisRequest(
        user_idea=idea,
        domain=ResearchDomain.LEARNING_ANALYTICS,
        max_results=max_results
    )
    
    try:
        if iterative:
            click.echo(f"Running iterative generation with {iterations} iterations...")
            responses = hypothesis_generator.iterative_generation(request, iterations)
            
            for i, response in enumerate(responses):
                click.echo(f"\n{'='*60}")
                click.echo(f"ITERATION {i+1}")
                click.echo(f"{'='*60}")
                _display_response(response)
        else:
            response = hypothesis_generator.generate_hypotheses(request)
            _display_response(response)
            
    except Exception as e:
        click.echo(f"Error generating hypotheses: {e}")


def _display_response(response):
    """Display hypothesis generation response."""
    click.echo(f"\nUser Idea: {response.user_idea}")
    click.echo(f"Confidence Score: {response.confidence_score:.3f}")
    
    click.echo(f"\nGenerated Hypotheses ({len(response.generated_hypotheses)}):")
    for i, hypothesis in enumerate(response.generated_hypotheses, 1):
        click.echo(f"{i}. {hypothesis}")
    
    if response.reasoning:
        click.echo(f"\nReasoning:\n{response.reasoning}")
    
    if response.follow_up_questions:
        click.echo(f"\nFollow-up Questions:")
        for i, question in enumerate(response.follow_up_questions, 1):
            click.echo(f"{i}. {question}")
    
    if response.suggested_searches:
        click.echo(f"\nSuggested Additional Searches:")
        for i, search in enumerate(response.suggested_searches, 1):
            click.echo(f"{i}. {search}")
    
    click.echo(f"\nRelevant Literature ({len(response.search_results)}):")
    for i, result in enumerate(response.search_results[:5], 1):  # Show top 5
        click.echo(f"{i}. [{result.source_type}] {result.document_id} (score: {result.score:.3f})")
        click.echo(f"   {result.content[:100]}...")


@cli.command()
def demo():
    """Run a demo with sample data."""
    click.echo("Running demo with sample data...")
    
    # Generate sample data
    from examples.sample_data_generator import save_sample_data
    papers = save_sample_data()
    
    # Build indices
    semantic_manager = SemanticSpaceManager()
    semantic_manager.build_from_literature(papers)
    
    # Save indices
    index_path = Path('data/indices')
    semantic_manager.save_all(str(index_path))
    
    # Initialize components
    openai_client = OpenAI(api_key=os.getenv('OPENAI_API_KEY'))
    retrieval_engine = RetrievalEngine(semantic_manager)
    hypothesis_generator = HypothesisGenerator(openai_client, retrieval_engine)
    
    # Demo idea
    demo_idea = "How can we use machine learning to predict student engagement in online courses?"
    
    request = HypothesisRequest(
        user_idea=demo_idea,
        domain=ResearchDomain.LEARNING_ANALYTICS,
        max_results=5
    )
    
    try:
        response = hypothesis_generator.generate_hypotheses(request)
        click.echo(f"\nDemo Results:")
        _display_response(response)
    except Exception as e:
        click.echo(f"Demo failed: {e}")


def main():
    """Main entry point."""
    cli()


if __name__ == '__main__':
    main()
