# 🚀 快速开始指南

本指南将帮助您快速设置和运行 Paper Hypothesis Generator 系统。

## 📋 前置要求

- Python 3.8+
- OpenAI API Key (用于假设生成)
- 8GB+ RAM (推荐，用于处理大型嵌入模型)

## ⚡ 5分钟快速体验

### 1. 基础设置

```bash
# 1. 克隆或下载项目到本地
cd paper_hgen

# 2. 创建虚拟环境 (推荐)
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate     # Windows

# 3. 安装基础依赖
pip install pydantic python-dotenv
```

### 2. 测试系统

```bash
# 运行系统测试
python test_system.py
```

如果看到 "✅ All tests passed!" 说明核心系统正常工作。

### 3. 体验示例数据

```bash
# 生成示例数据 (已包含3篇学习分析领域的论文)
python -c "import sys; sys.path.insert(0, '.'); from examples.sample_data_generator import save_sample_data; save_sample_data()"
```

### 4. 查看示例数据

生成的示例数据位于 `data/processed/` 目录：
- `sample_paper_1.json` - MOOC预测分析论文
- `sample_paper_2.json` - 自适应学习系统论文  
- `sample_paper_3.json` - 协作学习网络分析论文

每个文件包含：
- 论文元数据 (标题、作者、年份等)
- 研究问题 (从论文中提取的关键问题)
- 研究方法 (使用的方法和工具)

## 🔧 完整功能设置

### 1. 安装完整依赖

```bash
# 安装所有依赖 (需要网络连接)
pip install -r requirements.txt
```

### 2. 配置API密钥

```bash
# 复制环境配置文件
cp .env.example .env

# 编辑 .env 文件，添加您的 OpenAI API Key
# OPENAI_API_KEY=your_api_key_here
```

### 3. 处理PDF文献 (可选)

```bash
# 将PDF文件放入 data/pdfs/ 目录
# 然后提取文献信息
python -m paper_hgen.cli extract --pdf-dir data/pdfs --output-dir data/processed
```

### 4. 构建语义索引

```bash
# 基于处理的文献构建语义索引
python -m paper_hgen.cli build-index --data-dir data/processed --index-dir data/indices
```

### 5. 生成研究假设

```bash
# 使用命令行生成假设
python -m paper_hgen.cli generate --idea "如何使用机器学习预测学生在在线课程中的参与度？"

# 或运行演示
python -m paper_hgen.cli demo
```

### 6. 启动Web界面

```bash
# 安装 Streamlit
pip install streamlit

# 启动Web应用
streamlit run paper_hgen/web_app.py
```

然后在浏览器中访问 `http://localhost:8501`

## 📚 使用示例

### 命令行使用

```bash
# 基础假设生成
python -m paper_hgen.cli generate --idea "深度学习在教育评估中的应用"

# 迭代式生成 (3轮优化)
python -m paper_hgen.cli generate --idea "个性化学习路径推荐" --iterative --iterations 3

# 指定搜索结果数量
python -m paper_hgen.cli generate --idea "学习分析中的隐私保护" --max-results 15
```

### Python API使用

```python
import sys
sys.path.insert(0, '.')

from paper_hgen.models import HypothesisRequest, ResearchDomain
from examples.sample_data_generator import generate_sample_papers

# 生成示例数据
papers = generate_sample_papers()

# 创建假设生成请求
request = HypothesisRequest(
    user_idea="如何提高在线学习的互动性？",
    domain=ResearchDomain.LEARNING_ANALYTICS,
    max_results=5
)

print(f"用户想法: {request.user_idea}")
print(f"搜索域: {request.domain}")
print(f"最大结果数: {request.max_results}")
```

## 🎯 核心功能

### 1. 文献信息提取
- 从PDF自动提取研究问题和方法
- 支持中英文文献
- 提取置信度评分

### 2. 语义搜索
- **稠密搜索**: 基于语义相似性 (默认权重: 0.7)
- **稀疏搜索**: 基于关键词匹配 (默认权重: 0.3)
- 混合排序和结果融合

### 3. 假设生成
- 基于GPT-4o的智能生成
- 提供推理过程
- 生成后续研究问题
- 推荐相关搜索方向

### 4. 迭代优化
- 支持多轮假设优化
- 基于反馈的查询改进
- 避免重复结果

## 🔍 故障排除

### 常见问题

1. **导入错误**: 确保已安装必要依赖
   ```bash
   pip install pydantic python-dotenv
   ```

2. **API错误**: 检查 `.env` 文件中的 OpenAI API Key

3. **内存不足**: 减少 `max_results` 参数或使用更小的嵌入模型

4. **网络问题**: 某些依赖可能需要科学上网安装

### 最小化运行

如果只想体验核心功能，可以：

1. 只安装基础依赖: `pip install pydantic python-dotenv`
2. 运行测试: `python test_system.py`
3. 查看示例数据: `data/processed/sample_*.json`
4. 使用Python API进行基础操作

## 📈 下一步

1. **添加更多文献**: 将PDF文件放入 `data/pdfs/` 并运行提取
2. **自定义领域**: 修改 `ResearchDomain` 枚举添加新领域
3. **调整权重**: 在 `.env` 中调整 `DENSE_WEIGHT` 和 `SPARSE_WEIGHT`
4. **扩展提示**: 自定义 `reasoning.py` 中的生成提示

## 🤝 获取帮助

- 查看 `README.md` 了解详细信息
- 查看 `docs/API_GUIDE.md` 了解API使用
- 运行 `python -m paper_hgen.cli --help` 查看命令帮助
- 检查 `logs/` 目录中的日志文件

---

**提示**: 本系统目前专注于学习分析领域，但架构支持扩展到其他研究领域。
