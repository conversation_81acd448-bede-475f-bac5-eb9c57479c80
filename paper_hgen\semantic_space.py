"""
Semantic space implementations for dense and sparse retrieval.
"""

import pickle
import logging
from abc import ABC, abstractmethod
from typing import List, Dict, Any, Tu<PERSON>, Optional
from pathlib import Path

try:
    import numpy as np
    NUMPY_AVAILABLE = True
except ImportError:
    NUMPY_AVAILABLE = False

try:
    import faiss
    FAISS_AVAILABLE = True
except ImportError:
    FAISS_AVAILABLE = False

try:
    from sentence_transformers import SentenceTransformer
    SENTENCE_TRANSFORMERS_AVAILABLE = True
except ImportError:
    SENTENCE_TRANSFORMERS_AVAILABLE = False

try:
    from rank_bm25 import BM25Okapi
    BM25_AVAILABLE = True
except ImportError:
    BM25_AVAILABLE = False

try:
    import nltk
    from nltk.tokenize import word_tokenize
    from nltk.corpus import stopwords
    from nltk.stem import PorterStemmer
    NLTK_AVAILABLE = True
except ImportError:
    NLTK_AVAILABLE = False

from .models import LiteratureDocument, ResearchProblem, ResearchMethod

logger = logging.getLogger(__name__)

# Download required NLTK data
if NLTK_AVAILABLE:
    try:
        nltk.data.find('tokenizers/punkt')
    except LookupError:
        try:
            nltk.download('punkt')
        except:
            pass

    try:
        nltk.data.find('corpora/stopwords')
    except LookupError:
        try:
            nltk.download('stopwords')
        except:
            pass


class SemanticSpace(ABC):
    """Abstract base class for semantic spaces."""
    
    def __init__(self, space_type: str):
        self.space_type = space_type
        self.documents = []
        self.metadata = []
        
    @abstractmethod
    def build_index(self, documents: List[Dict[str, Any]]) -> None:
        """Build the semantic space index from documents."""
        pass
    
    @abstractmethod
    def search(self, query: str, top_k: int = 10) -> List[Tuple[int, float]]:
        """Search the semantic space and return (doc_id, score) pairs."""
        pass
    
    @abstractmethod
    def save(self, path: str) -> None:
        """Save the semantic space to disk."""
        pass
    
    @abstractmethod
    def load(self, path: str) -> None:
        """Load the semantic space from disk."""
        pass


class DenseSpace(SemanticSpace):
    """Dense semantic space using sentence transformers and FAISS."""
    
    def __init__(self, model_name: str = "BAAI/bge-large-zh-v1.5"):
        super().__init__("dense")
        self.model_name = model_name
        self.encoder = None
        self.index = None
        self.dimension = None
        
    def _initialize_encoder(self):
        """Initialize the sentence transformer model."""
        if not SENTENCE_TRANSFORMERS_AVAILABLE:
            raise ImportError("sentence-transformers is not available")
        if self.encoder is None:
            logger.info(f"Loading embedding model: {self.model_name}")
            self.encoder = SentenceTransformer(self.model_name)
            self.dimension = self.encoder.get_sentence_embedding_dimension()
    
    def build_index(self, documents: List[Dict[str, Any]]) -> None:
        """Build FAISS index from documents."""
        if not FAISS_AVAILABLE:
            raise ImportError("faiss is not available")
        if not NUMPY_AVAILABLE:
            raise ImportError("numpy is not available")

        self._initialize_encoder()

        self.documents = documents
        self.metadata = [doc.get('metadata', {}) for doc in documents]

        # Extract text content for embedding
        texts = []
        for doc in documents:
            content = doc.get('content', '')
            if not content and 'text' in doc:
                content = doc['text']
            texts.append(content)

        logger.info(f"Encoding {len(texts)} documents...")
        embeddings = self.encoder.encode(texts, show_progress_bar=True)

        # Build FAISS index
        self.index = faiss.IndexFlatIP(self.dimension)  # Inner product for cosine similarity

        # Normalize embeddings for cosine similarity
        faiss.normalize_L2(embeddings)
        self.index.add(embeddings.astype(np.float32))

        logger.info(f"Built dense index with {self.index.ntotal} vectors")
    
    def search(self, query: str, top_k: int = 10) -> List[Tuple[int, float]]:
        """Search using dense embeddings."""
        if self.index is None or self.encoder is None:
            raise ValueError("Index not built. Call build_index() first.")
        
        # Encode query
        query_embedding = self.encoder.encode([query])
        faiss.normalize_L2(query_embedding)
        
        # Search
        scores, indices = self.index.search(query_embedding.astype(np.float32), top_k)
        
        results = []
        for i, (idx, score) in enumerate(zip(indices[0], scores[0])):
            if idx != -1:  # Valid result
                results.append((int(idx), float(score)))
        
        return results
    
    def save(self, path: str) -> None:
        """Save the dense space to disk."""
        save_path = Path(path)
        save_path.mkdir(parents=True, exist_ok=True)
        
        # Save FAISS index
        if self.index is not None:
            faiss.write_index(self.index, str(save_path / "dense_index.faiss"))
        
        # Save metadata
        metadata = {
            'model_name': self.model_name,
            'dimension': self.dimension,
            'documents': self.documents,
            'metadata': self.metadata
        }
        
        with open(save_path / "dense_metadata.pkl", 'wb') as f:
            pickle.dump(metadata, f)
        
        logger.info(f"Saved dense space to {save_path}")
    
    def load(self, path: str) -> None:
        """Load the dense space from disk."""
        load_path = Path(path)
        
        # Load FAISS index
        index_path = load_path / "dense_index.faiss"
        if index_path.exists():
            self.index = faiss.read_index(str(index_path))
        
        # Load metadata
        metadata_path = load_path / "dense_metadata.pkl"
        if metadata_path.exists():
            with open(metadata_path, 'rb') as f:
                metadata = pickle.load(f)
            
            self.model_name = metadata['model_name']
            self.dimension = metadata['dimension']
            self.documents = metadata['documents']
            self.metadata = metadata['metadata']
            
            # Initialize encoder
            self._initialize_encoder()
        
        logger.info(f"Loaded dense space from {load_path}")


class SparseSpace(SemanticSpace):
    """Sparse semantic space using BM25."""
    
    def __init__(self, language: str = "english"):
        super().__init__("sparse")
        self.language = language
        self.bm25 = None
        self.tokenized_docs = []

        if not BM25_AVAILABLE:
            raise ImportError("rank-bm25 is not available")
        if not NLTK_AVAILABLE:
            raise ImportError("nltk is not available")

        self.stemmer = PorterStemmer()

        # Initialize stopwords
        try:
            self.stop_words = set(stopwords.words(language))
        except:
            self.stop_words = set()
    
    def _preprocess_text(self, text: str) -> List[str]:
        """Preprocess text for BM25 indexing."""
        # Tokenize
        tokens = word_tokenize(text.lower())
        
        # Remove stopwords and non-alphabetic tokens
        tokens = [
            self.stemmer.stem(token) 
            for token in tokens 
            if token.isalpha() and token not in self.stop_words
        ]
        
        return tokens

    def build_index(self, documents: List[Dict[str, Any]]) -> None:
        """Build BM25 index from documents."""
        self.documents = documents
        self.metadata = [doc.get('metadata', {}) for doc in documents]

        # Extract and preprocess text content
        texts = []
        for doc in documents:
            content = doc.get('content', '')
            if not content and 'text' in doc:
                content = doc['text']
            texts.append(content)

        # Tokenize all documents
        self.tokenized_docs = [self._preprocess_text(text) for text in texts]

        # Build BM25 index
        self.bm25 = BM25Okapi(self.tokenized_docs)

        logger.info(f"Built sparse index with {len(self.tokenized_docs)} documents")

    def search(self, query: str, top_k: int = 10) -> List[Tuple[int, float]]:
        """Search using BM25."""
        if self.bm25 is None:
            raise ValueError("Index not built. Call build_index() first.")

        # Preprocess query
        query_tokens = self._preprocess_text(query)

        # Get BM25 scores
        scores = self.bm25.get_scores(query_tokens)

        # Get top-k results
        if not NUMPY_AVAILABLE:
            # Fallback without numpy
            indexed_scores = [(i, score) for i, score in enumerate(scores) if score > 0]
            indexed_scores.sort(key=lambda x: x[1], reverse=True)
            results = indexed_scores[:top_k]
        else:
            top_indices = np.argsort(scores)[::-1][:top_k]
            results = [(int(idx), float(scores[idx])) for idx in top_indices if scores[idx] > 0]

        return results

    def save(self, path: str) -> None:
        """Save the sparse space to disk."""
        save_path = Path(path)
        save_path.mkdir(parents=True, exist_ok=True)

        # Save BM25 and related data
        sparse_data = {
            'bm25': self.bm25,
            'tokenized_docs': self.tokenized_docs,
            'documents': self.documents,
            'metadata': self.metadata,
            'language': self.language
        }

        with open(save_path / "sparse_data.pkl", 'wb') as f:
            pickle.dump(sparse_data, f)

        logger.info(f"Saved sparse space to {save_path}")

    def load(self, path: str) -> None:
        """Load the sparse space from disk."""
        load_path = Path(path)
        sparse_path = load_path / "sparse_data.pkl"

        if sparse_path.exists():
            with open(sparse_path, 'rb') as f:
                sparse_data = pickle.load(f)

            self.bm25 = sparse_data['bm25']
            self.tokenized_docs = sparse_data['tokenized_docs']
            self.documents = sparse_data['documents']
            self.metadata = sparse_data['metadata']
            self.language = sparse_data.get('language', 'english')

        logger.info(f"Loaded sparse space from {load_path}")


class SemanticSpaceManager:
    """Manager for multiple semantic spaces (problems and methods)."""

    def __init__(self, dense_model: str = "BAAI/bge-large-zh-v1.5"):
        self.dense_model = dense_model
        self.spaces = {
            'problems_dense': DenseSpace(dense_model),
            'problems_sparse': SparseSpace(),
            'methods_dense': DenseSpace(dense_model),
            'methods_sparse': SparseSpace()
        }

    def build_from_literature(self, literature_docs: List[LiteratureDocument]) -> None:
        """Build all semantic spaces from literature documents."""
        # Prepare problem documents
        problem_docs = []
        for lit_doc in literature_docs:
            for problem in lit_doc.research_problems:
                problem_docs.append({
                    'content': f"{problem.text} {problem.context}",
                    'text': problem.text,
                    'metadata': {
                        'id': problem.id,
                        'paper_id': problem.paper_id,
                        'keywords': problem.keywords,
                        'confidence': problem.confidence,
                        'type': 'problem'
                    }
                })

        # Prepare method documents
        method_docs = []
        for lit_doc in literature_docs:
            for method in lit_doc.research_methods:
                method_docs.append({
                    'content': f"{method.name} {method.description}",
                    'text': f"{method.name}: {method.description}",
                    'metadata': {
                        'id': method.id,
                        'paper_id': method.paper_id,
                        'methodology_type': method.methodology_type,
                        'tools_used': method.tools_used,
                        'confidence': method.confidence,
                        'type': 'method'
                    }
                })

        # Build indices
        logger.info("Building problem semantic spaces...")
        if problem_docs:
            self.spaces['problems_dense'].build_index(problem_docs)
            self.spaces['problems_sparse'].build_index(problem_docs)

        logger.info("Building method semantic spaces...")
        if method_docs:
            self.spaces['methods_dense'].build_index(method_docs)
            self.spaces['methods_sparse'].build_index(method_docs)

        logger.info("Semantic space construction completed")

    def save_all(self, base_path: str) -> None:
        """Save all semantic spaces."""
        base_path = Path(base_path)
        for space_name, space in self.spaces.items():
            space.save(base_path / space_name)

    def load_all(self, base_path: str) -> None:
        """Load all semantic spaces."""
        base_path = Path(base_path)
        for space_name, space in self.spaces.items():
            space_path = base_path / space_name
            if space_path.exists():
                space.load(space_path)

    def get_space(self, space_type: str, content_type: str) -> SemanticSpace:
        """Get a specific semantic space."""
        space_name = f"{content_type}_{space_type}"
        return self.spaces.get(space_name)
