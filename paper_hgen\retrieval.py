"""
Retrieval engine for hybrid dense and sparse search.
"""

import logging
from typing import List, Dict, Any, Tuple, Optional
from dataclasses import dataclass
import numpy as np

from .semantic_space import SemanticSpaceManager, SemanticSpace
from .models import SearchResult, LiteratureDocument

logger = logging.getLogger(__name__)


@dataclass
class RetrievalConfig:
    """Configuration for retrieval engine."""
    dense_weight: float = 0.7
    sparse_weight: float = 0.3
    max_results: int = 10
    min_score_threshold: float = 0.1
    normalize_scores: bool = True


class RetrievalEngine:
    """Hybrid retrieval engine combining dense and sparse search."""
    
    def __init__(self, semantic_manager: SemanticSpaceManager, config: Optional[RetrievalConfig] = None):
        self.semantic_manager = semantic_manager
        self.config = config or RetrievalConfig()
        
    def search(self, query: str, search_types: List[str] = None, **kwargs) -> List[SearchResult]:
        """
        Perform hybrid search across specified content types.
        
        Args:
            query: Search query
            search_types: List of content types to search ['problems', 'methods']
            **kwargs: Additional search parameters
        
        Returns:
            List of SearchResult objects
        """
        if search_types is None:
            search_types = ['problems', 'methods']
        
        # Override config with kwargs
        config = RetrievalConfig(
            dense_weight=kwargs.get('dense_weight', self.config.dense_weight),
            sparse_weight=kwargs.get('sparse_weight', self.config.sparse_weight),
            max_results=kwargs.get('max_results', self.config.max_results),
            min_score_threshold=kwargs.get('min_score_threshold', self.config.min_score_threshold),
            normalize_scores=kwargs.get('normalize_scores', self.config.normalize_scores)
        )
        
        all_results = []
        
        for content_type in search_types:
            # Get dense and sparse results
            dense_results = self._search_dense(query, content_type, config.max_results * 2)
            sparse_results = self._search_sparse(query, content_type, config.max_results * 2)
            
            # Combine and rank results
            combined_results = self._combine_results(
                dense_results, sparse_results, content_type, config
            )
            
            all_results.extend(combined_results)
        
        # Sort by final score and limit results
        all_results.sort(key=lambda x: x.score, reverse=True)
        return all_results[:config.max_results]
    
    def _search_dense(self, query: str, content_type: str, top_k: int) -> List[Tuple[int, float]]:
        """Search using dense embeddings."""
        dense_space = self.semantic_manager.get_space('dense', content_type)
        if dense_space is None or dense_space.index is None:
            logger.warning(f"Dense space for {content_type} not available")
            return []
        
        try:
            return dense_space.search(query, top_k)
        except Exception as e:
            logger.error(f"Dense search failed for {content_type}: {e}")
            return []
    
    def _search_sparse(self, query: str, content_type: str, top_k: int) -> List[Tuple[int, float]]:
        """Search using sparse (BM25) method."""
        sparse_space = self.semantic_manager.get_space('sparse', content_type)
        if sparse_space is None or sparse_space.bm25 is None:
            logger.warning(f"Sparse space for {content_type} not available")
            return []
        
        try:
            return sparse_space.search(query, top_k)
        except Exception as e:
            logger.error(f"Sparse search failed for {content_type}: {e}")
            return []
    
    def _combine_results(
        self, 
        dense_results: List[Tuple[int, float]], 
        sparse_results: List[Tuple[int, float]], 
        content_type: str,
        config: RetrievalConfig
    ) -> List[SearchResult]:
        """Combine dense and sparse results with weighted scoring."""
        
        # Normalize scores if requested
        if config.normalize_scores:
            dense_results = self._normalize_scores(dense_results)
            sparse_results = self._normalize_scores(sparse_results)
        
        # Create score dictionary for combination
        combined_scores = {}
        
        # Add dense scores
        for doc_id, score in dense_results:
            combined_scores[doc_id] = config.dense_weight * score
        
        # Add sparse scores
        for doc_id, score in sparse_results:
            if doc_id in combined_scores:
                combined_scores[doc_id] += config.sparse_weight * score
            else:
                combined_scores[doc_id] = config.sparse_weight * score
        
        # Convert to SearchResult objects
        search_results = []
        space = self.semantic_manager.get_space('dense', content_type)  # Use dense space for metadata
        
        for doc_id, final_score in combined_scores.items():
            if final_score < config.min_score_threshold:
                continue
                
            if doc_id < len(space.documents):
                doc = space.documents[doc_id]
                metadata = space.metadata[doc_id] if doc_id < len(space.metadata) else {}
                
                search_result = SearchResult(
                    document_id=metadata.get('id', f"{content_type}_{doc_id}"),
                    score=final_score,
                    content=doc.get('content', doc.get('text', '')),
                    metadata=metadata,
                    source_type=content_type
                )
                search_results.append(search_result)
        
        return search_results
    
    def _normalize_scores(self, results: List[Tuple[int, float]]) -> List[Tuple[int, float]]:
        """Normalize scores to [0, 1] range."""
        if not results:
            return results
        
        scores = [score for _, score in results]
        min_score = min(scores)
        max_score = max(scores)
        
        if max_score == min_score:
            return [(doc_id, 1.0) for doc_id, _ in results]
        
        normalized = []
        for doc_id, score in results:
            norm_score = (score - min_score) / (max_score - min_score)
            normalized.append((doc_id, norm_score))
        
        return normalized
    
    def search_similar_problems(self, query: str, **kwargs) -> List[SearchResult]:
        """Search for similar research problems."""
        return self.search(query, search_types=['problems'], **kwargs)
    
    def search_similar_methods(self, query: str, **kwargs) -> List[SearchResult]:
        """Search for similar research methods."""
        return self.search(query, search_types=['methods'], **kwargs)
    
    def get_document_details(self, document_id: str, content_type: str) -> Optional[Dict[str, Any]]:
        """Get detailed information about a specific document."""
        space = self.semantic_manager.get_space('dense', content_type)
        if space is None:
            return None
        
        for i, metadata in enumerate(space.metadata):
            if metadata.get('id') == document_id:
                return {
                    'document': space.documents[i],
                    'metadata': metadata,
                    'index': i
                }
        
        return None


class IterativeRetrieval:
    """Support for iterative retrieval based on LLM feedback."""
    
    def __init__(self, retrieval_engine: RetrievalEngine):
        self.retrieval_engine = retrieval_engine
        self.search_history = []
    
    def search_with_context(
        self, 
        query: str, 
        previous_results: List[SearchResult] = None,
        refinement_feedback: str = None,
        **kwargs
    ) -> List[SearchResult]:
        """
        Perform search with context from previous iterations.
        
        Args:
            query: Current search query
            previous_results: Results from previous searches
            refinement_feedback: Feedback for query refinement
            **kwargs: Additional search parameters
        """
        # Store search in history
        search_entry = {
            'query': query,
            'refinement_feedback': refinement_feedback,
            'previous_results_count': len(previous_results) if previous_results else 0
        }
        
        # Refine query based on feedback and context
        refined_query = self._refine_query(query, previous_results, refinement_feedback)
        
        # Perform search
        results = self.retrieval_engine.search(refined_query, **kwargs)
        
        # Filter out previously seen results if requested
        if previous_results and kwargs.get('avoid_duplicates', True):
            seen_ids = {result.document_id for result in previous_results}
            results = [r for r in results if r.document_id not in seen_ids]
        
        search_entry['results_count'] = len(results)
        search_entry['refined_query'] = refined_query
        self.search_history.append(search_entry)
        
        return results
    
    def _refine_query(
        self, 
        query: str, 
        previous_results: List[SearchResult] = None,
        refinement_feedback: str = None
    ) -> str:
        """Refine query based on context and feedback."""
        refined_query = query
        
        # Add refinement based on feedback
        if refinement_feedback:
            refined_query += f" {refinement_feedback}"
        
        # Add context from previous results (extract key terms)
        if previous_results:
            # Extract keywords from top results
            keywords = set()
            for result in previous_results[:3]:  # Use top 3 results
                if 'keywords' in result.metadata:
                    keywords.update(result.metadata['keywords'])
            
            if keywords:
                keyword_str = " ".join(list(keywords)[:5])  # Limit to 5 keywords
                refined_query += f" {keyword_str}"
        
        return refined_query
    
    def get_search_history(self) -> List[Dict[str, Any]]:
        """Get the search history for this session."""
        return self.search_history.copy()
    
    def clear_history(self) -> None:
        """Clear the search history."""
        self.search_history.clear()
