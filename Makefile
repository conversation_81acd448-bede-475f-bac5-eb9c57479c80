# Makefile for Paper Hypothesis Generator

.PHONY: help install setup test clean lint format run-cli run-web demo build docs

# Default target
help:
	@echo "Paper Hypothesis Generator - Available commands:"
	@echo ""
	@echo "Setup and Installation:"
	@echo "  install     - Install dependencies"
	@echo "  setup       - Run full setup including NLTK data"
	@echo ""
	@echo "Development:"
	@echo "  test        - Run tests"
	@echo "  lint        - Run linting"
	@echo "  format      - Format code"
	@echo "  clean       - Clean temporary files"
	@echo ""
	@echo "Running:"
	@echo "  run-cli     - Run CLI interface"
	@echo "  run-web     - Run web interface"
	@echo "  demo        - Run demo with sample data"
	@echo ""
	@echo "Data Processing:"
	@echo "  extract     - Extract information from PDFs"
	@echo "  build-index - Build semantic indices"
	@echo ""
	@echo "Documentation:"
	@echo "  docs        - Generate documentation"
	@echo ""

# Installation and setup
install:
	pip install --upgrade pip
	pip install -r requirements.txt

setup:
	python scripts/setup.py

# Development
test:
	pytest tests/ -v --cov=paper_hgen --cov-report=html --cov-report=term

test-fast:
	pytest tests/ -v -x

lint:
	flake8 paper_hgen tests scripts
	black --check paper_hgen tests scripts

format:
	black paper_hgen tests scripts
	isort paper_hgen tests scripts

clean:
	find . -type f -name "*.pyc" -delete
	find . -type d -name "__pycache__" -delete
	find . -type d -name "*.egg-info" -exec rm -rf {} +
	rm -rf build/
	rm -rf dist/
	rm -rf .pytest_cache/
	rm -rf htmlcov/
	rm -rf .coverage

# Running applications
run-cli:
	python -m paper_hgen.cli

run-web:
	streamlit run paper_hgen/web_app.py

demo:
	python -m paper_hgen.cli demo

# Data processing
extract:
	python -m paper_hgen.cli extract --pdf-dir data/pdfs --output-dir data/processed

build-index:
	python -m paper_hgen.cli build-index --data-dir data/processed --index-dir data/indices

# Generate sample data
sample-data:
	python examples/sample_data_generator.py

# Documentation
docs:
	@echo "Generating documentation..."
	@echo "README.md - Main documentation"
	@echo "docs/API_GUIDE.md - API usage guide"

# Build package
build:
	python -m build

# Install in development mode
dev-install:
	pip install -e .

# Check configuration
check-config:
	python -c "from paper_hgen.config import validate_config, get_config; print('Config valid:', validate_config(get_config()))"

# Download models (if needed)
download-models:
	python -c "from sentence_transformers import SentenceTransformer; SentenceTransformer('BAAI/bge-large-zh-v1.5')"

# Full workflow example
workflow: sample-data build-index
	@echo "Running full workflow with sample data..."
	python -m paper_hgen.cli generate --idea "How can we predict student engagement in online courses?" --max-results 5

# Docker commands (if Docker is used)
docker-build:
	docker build -t paper-hgen .

docker-run:
	docker run -p 8501:8501 paper-hgen

# Environment setup
env-setup:
	cp .env.example .env
	@echo "Please edit .env file with your configuration"

# Quality checks
quality: lint test
	@echo "All quality checks passed!"

# Release preparation
release-prep: clean format lint test build
	@echo "Release preparation complete!"

# Help for specific commands
help-cli:
	python -m paper_hgen.cli --help

help-extract:
	python -m paper_hgen.cli extract --help

help-generate:
	python -m paper_hgen.cli generate --help

# Performance testing
perf-test:
	python -m pytest tests/ -v -k "performance" --benchmark-only

# Security check
security:
	safety check
	bandit -r paper_hgen/

# Update dependencies
update-deps:
	pip-compile requirements.in
	pip install -r requirements.txt

# Backup data
backup-data:
	tar -czf backup_$(shell date +%Y%m%d_%H%M%S).tar.gz data/

# Restore from backup
restore-data:
	@echo "Usage: make restore-data BACKUP=backup_file.tar.gz"
	@if [ -n "$(BACKUP)" ]; then \
		tar -xzf $(BACKUP); \
		echo "Data restored from $(BACKUP)"; \
	else \
		echo "Please specify BACKUP=filename"; \
	fi
