"""
Hypothesis generation and reasoning module using LLM.
"""

import json
import logging
import uuid
from typing import List, Dict, Any, Optional
from datetime import datetime
from openai import OpenAI

from .models import HypothesisRequest, HypothesisResponse, SearchResult
from .retrieval import RetrievalEngine, IterativeRetrieval

logger = logging.getLogger(__name__)


class HypothesisGenerator:
    """Generate research hypotheses using LLM reasoning and literature retrieval."""
    
    def __init__(
        self, 
        openai_client: OpenAI, 
        retrieval_engine: RetrievalEngine,
        model: str = "gpt-4o",
        max_context_length: int = 8000
    ):
        self.client = openai_client
        self.retrieval_engine = retrieval_engine
        self.model = model
        self.max_context_length = max_context_length
        self.iterative_retrieval = IterativeRetrieval(retrieval_engine)
    
    def generate_hypotheses(self, request: HypothesisRequest) -> HypothesisResponse:
        """Generate research hypotheses based on user idea and literature."""
        request_id = str(uuid.uuid4())
        
        # Initial literature search
        search_results = self.retrieval_engine.search(
            request.user_idea,
            max_results=request.max_results,
            dense_weight=request.search_weights.get('dense', 0.7),
            sparse_weight=request.search_weights.get('sparse', 0.3)
        )
        
        # Generate initial hypotheses
        hypotheses, reasoning = self._generate_initial_hypotheses(
            request.user_idea, search_results, request.generation_params
        )
        
        # Generate follow-up questions and search suggestions
        follow_up_questions = self._generate_follow_up_questions(
            request.user_idea, search_results, hypotheses
        )
        
        suggested_searches = self._generate_search_suggestions(
            request.user_idea, search_results
        )
        
        # Calculate confidence score
        confidence_score = self._calculate_confidence(search_results, hypotheses)
        
        return HypothesisResponse(
            request_id=request_id,
            user_idea=request.user_idea,
            search_results=search_results,
            generated_hypotheses=hypotheses,
            reasoning=reasoning,
            follow_up_questions=follow_up_questions,
            suggested_searches=suggested_searches,
            confidence_score=confidence_score
        )
    
    def iterative_generation(
        self, 
        initial_request: HypothesisRequest,
        iterations: int = 3
    ) -> List[HypothesisResponse]:
        """Perform iterative hypothesis generation with refinement."""
        responses = []
        current_request = initial_request
        previous_results = []
        
        for i in range(iterations):
            logger.info(f"Iteration {i+1}/{iterations}")
            
            # Generate hypotheses for current iteration
            response = self.generate_hypotheses(current_request)
            responses.append(response)
            
            # Prepare for next iteration if not the last one
            if i < iterations - 1:
                # Use follow-up questions to refine the search
                if response.follow_up_questions:
                    refined_idea = f"{current_request.user_idea} {response.follow_up_questions[0]}"
                else:
                    refined_idea = current_request.user_idea
                
                # Create new request with refined idea
                current_request = HypothesisRequest(
                    user_idea=refined_idea,
                    domain=current_request.domain,
                    max_results=current_request.max_results,
                    search_weights=current_request.search_weights,
                    generation_params=current_request.generation_params
                )
                
                previous_results.extend(response.search_results)
        
        return responses
    
    def _generate_initial_hypotheses(
        self, 
        user_idea: str, 
        search_results: List[SearchResult],
        generation_params: Dict[str, Any]
    ) -> tuple[List[str], str]:
        """Generate initial hypotheses using LLM."""
        
        # Prepare context from search results
        context = self._prepare_context(search_results)
        
        prompt = f"""
        You are a research expert in learning analytics and educational technology. 
        A researcher has the following idea or question:
        
        "{user_idea}"
        
        Based on the following relevant literature findings, generate 3-5 specific, testable research hypotheses:
        
        {context}
        
        Please provide:
        1. Specific, testable hypotheses that build upon or extend the existing literature
        2. Clear reasoning for each hypothesis based on the literature
        3. Identification of gaps or opportunities for novel research
        
        Format your response as JSON:
        {{
            "hypotheses": [
                "Hypothesis 1: Clear, testable statement",
                "Hypothesis 2: Clear, testable statement",
                ...
            ],
            "reasoning": "Detailed reasoning process explaining how you derived these hypotheses from the literature and user idea"
        }}
        
        Ensure hypotheses are:
        - Specific and measurable
        - Grounded in the provided literature
        - Novel or extending existing work
        - Relevant to the learning analytics domain
        """
        
        try:
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[{"role": "user", "content": prompt}],
                temperature=generation_params.get('temperature', 0.7),
                max_tokens=generation_params.get('max_tokens', 2000)
            )
            
            result = json.loads(response.choices[0].message.content)
            return result.get('hypotheses', []), result.get('reasoning', '')
            
        except Exception as e:
            logger.error(f"Failed to generate hypotheses: {e}")
            return [], f"Error in hypothesis generation: {str(e)}"
    
    def _generate_follow_up_questions(
        self, 
        user_idea: str, 
        search_results: List[SearchResult],
        hypotheses: List[str]
    ) -> List[str]:
        """Generate follow-up questions for further exploration."""
        
        context = self._prepare_context(search_results[:5])  # Use top 5 results
        
        prompt = f"""
        Based on the user's research idea: "{user_idea}"
        
        And the generated hypotheses:
        {chr(10).join(f"- {h}" for h in hypotheses)}
        
        Generate 3-5 follow-up questions that would help:
        1. Refine or validate the hypotheses
        2. Identify additional relevant literature
        3. Explore related research directions
        4. Address potential limitations or confounding factors
        
        Return as a JSON list: ["Question 1", "Question 2", ...]
        """
        
        try:
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[{"role": "user", "content": prompt}],
                temperature=0.6,
                max_tokens=800
            )
            
            return json.loads(response.choices[0].message.content)
            
        except Exception as e:
            logger.error(f"Failed to generate follow-up questions: {e}")
            return []
    
    def _generate_search_suggestions(
        self, 
        user_idea: str, 
        search_results: List[SearchResult]
    ) -> List[str]:
        """Generate suggestions for additional searches."""
        
        # Extract keywords and themes from search results
        keywords = set()
        for result in search_results[:5]:
            if 'keywords' in result.metadata:
                keywords.update(result.metadata['keywords'])
        
        prompt = f"""
        Based on the research idea: "{user_idea}"
        
        And these keywords from relevant literature: {list(keywords)[:10]}
        
        Suggest 3-5 additional search terms or phrases that might uncover:
        1. Related methodologies
        2. Alternative theoretical frameworks
        3. Recent developments in the field
        4. Cross-disciplinary approaches
        
        Return as a JSON list: ["Search term 1", "Search term 2", ...]
        """
        
        try:
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[{"role": "user", "content": prompt}],
                temperature=0.5,
                max_tokens=500
            )
            
            return json.loads(response.choices[0].message.content)
            
        except Exception as e:
            logger.error(f"Failed to generate search suggestions: {e}")
            return []
    
    def _prepare_context(self, search_results: List[SearchResult]) -> str:
        """Prepare literature context for LLM prompt."""
        context_parts = []
        current_length = 0
        
        for i, result in enumerate(search_results):
            # Create context entry
            entry = f"""
            [{i+1}] {result.source_type.upper()}: {result.document_id}
            Content: {result.content[:500]}...
            Relevance Score: {result.score:.3f}
            Keywords: {result.metadata.get('keywords', [])}
            """
            
            # Check if adding this entry would exceed context length
            if current_length + len(entry) > self.max_context_length:
                break
            
            context_parts.append(entry)
            current_length += len(entry)
        
        return "\n".join(context_parts)
    
    def _calculate_confidence(
        self, 
        search_results: List[SearchResult], 
        hypotheses: List[str]
    ) -> float:
        """Calculate confidence score for the generated hypotheses."""
        if not search_results or not hypotheses:
            return 0.0
        
        # Factors affecting confidence:
        # 1. Number and quality of search results
        # 2. Average relevance scores
        # 3. Number of hypotheses generated
        
        avg_score = sum(r.score for r in search_results) / len(search_results)
        result_factor = min(len(search_results) / 10.0, 1.0)  # Normalize to max 10 results
        hypothesis_factor = min(len(hypotheses) / 5.0, 1.0)  # Normalize to max 5 hypotheses
        
        confidence = (avg_score * 0.5 + result_factor * 0.3 + hypothesis_factor * 0.2)
        return min(confidence, 1.0)
