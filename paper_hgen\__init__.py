"""
Paper Hypothesis Generator

A system for generating research hypotheses based on literature analysis
using semantic spaces and LLM reasoning.
"""

__version__ = "0.1.0"
__author__ = "Research Team"
__email__ = "<EMAIL>"

from .models import (
    PaperMetadata,
    ResearchProblem,
    ResearchMethod,
    LiteratureDocument,
    HypothesisRequest,
    HypothesisResponse,
)

from .extractors import PDFExtractor, LiteratureExtractor
from .semantic_space import SemanticSpace, DenseSpace, SparseSpace
from .retrieval import RetrievalEngine
from .reasoning import HypothesisGenerator

__all__ = [
    "PaperMetadata",
    "ResearchProblem", 
    "ResearchMethod",
    "LiteratureDocument",
    "HypothesisRequest",
    "HypothesisResponse",
    "PDFExtractor",
    "LiteratureExtractor",
    "SemanticSpace",
    "DenseSpace",
    "SparseSpace",
    "RetrievalEngine",
    "HypothesisGenerator",
]
